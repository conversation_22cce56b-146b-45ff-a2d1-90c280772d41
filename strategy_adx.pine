//@version=6
strategy("ADX x HMA", overlay=true, fill_orders_on_standard_ohlc = true)

adxlen = input(14, title="ADX Smoothing")
dilen = input(14, title="DI Length")
hmalen = input(55, title="HMA Length")

hma = ta.hma(close, hmalen)

dirmov(len) =>
  up = ta.change(high)
  down = -ta.change(low)
  plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
  minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
  truerange = ta.rma(ta.tr, len)
  plus = fixnan(100 * ta.rma(plusDM, len) / truerange)
  minus = fixnan(100 * ta.rma(minusDM, len) / truerange)
  [plus, minus]
adx(dilen, adxlen) =>
  [plus, minus] = dirmov(dilen)
  sum = plus + minus
  adx = 100 * ta.rma(math.abs(plus - minus) / (sum == 0 ? 1 : sum), adxlen)
sig = adx(dilen, adxlen)
// plot(sig, color=color.red, title="ADX")

longCondition = sig[0] < 14 and sig[0] > sig[1] and sig[1] < sig[2] and sig[2] > sig[3] and sig[3] > sig[4] and hma[0] > hma[1] and hma[1] > hma[2]

if (strategy.position_size == 0 and longCondition)
    strategy.entry("long", strategy.long)

shortCondition = sig[0] < 14 and sig[0] > sig[1] and sig[1] < sig[2] and sig[2] > sig[3] and sig[3] > sig[4] and hma[0] < hma[1] and hma[1] < hma[2]

if (strategy.position_size == 0 and shortCondition)
    strategy.entry("short", strategy.short)

longCloseCondition = sig[0] < sig[1] and sig[1] < sig[2] and sig[2] < sig[3]
if (strategy.position_size > 0 and longCloseCondition)
    strategy.exit(id='close long', stop=close, from_entry='long')  

shortCloseCondition = sig[0] < sig[1] and sig[1] < sig[2] and sig[2] < sig[3]
if (strategy.position_size < 0 and shortCloseCondition)
    strategy.exit(id='close short', stop=close, from_entry='short')  
