//@version=6
indicator(title="Moving Average Convergence Divergence", shorttitle="MACD123", timeframe="", timeframe_gaps=true)
// Getting inputs
fast_length = input(title = "Fast Length", defval = 12)
slow_length = input(title = "Slow Length", defval = 26)
src = input(title = "Source", defval = close)
signal_length = input.int(title = "Signal Smoothing",  minval = 1, maxval = 50, defval = 9, display = display.data_window)
sma_source = input.string(title = "Oscillator MA Type",  defval = "EMA", options = ["SMA", "EMA"], display = display.data_window)
sma_signal = input.string(title = "Signal Line MA Type", defval = "EMA", options = ["SMA", "EMA"], display = display.data_window)

// StochRSI inputs
srsi_lengthRSI = input(14, title="RSI Length")
srsi_lengthStoch = input(14, title="Stochastic Length")
srsi_smoothK = input(3, title="Stochastic %K")
srsi_smoothD = input(3, title="Stochastic %D")

// Calculating MACD
fast_ma = sma_source == "SMA" ? ta.sma(src, fast_length) : ta.ema(src, fast_length)
slow_ma = sma_source == "SMA" ? ta.sma(src, slow_length) : ta.ema(src, slow_length)
macd = fast_ma - slow_ma
signal = sma_signal == "SMA" ? ta.sma(macd, signal_length) : ta.ema(macd, signal_length)
hist = macd - signal

// Calculating StochRSI
srsi_rsi = ta.rsi(src, srsi_lengthRSI)
srsi_stoch = ta.stoch(srsi_rsi, srsi_rsi, srsi_rsi, srsi_lengthStoch)
srsi_k = ta.sma(srsi_stoch, srsi_smoothK)

// Plot X marks for extreme MACD values relative to price
macd_price_ratio = macd / src * 100
plotshape( (macd_price_ratio < -2 and srsi_k < 10) or (macd_price_ratio > 2 and srsi_k > 90), title="Extreme MACD", style=shape.xcross, location=location.top, color=color.yellow, size=size.small)

// alertcondition(hist[1] >= 0 and hist < 0, title = 'Rising to falling', message = 'The MACD histogram switched from a rising to falling state')
// alertcondition(hist[1] <= 0 and hist > 0, title = 'Falling to rising', message = 'The MACD histogram switched from a falling to rising state')

hline(0, "Zero Line", color = color.new(#787B86, 50))
plot(hist, title = "Histogram", style = plot.style_columns, color = (hist >= 0 ? (hist[1] < hist ? #26A69A : #B2DFDB) : (hist[1] < hist ? #FFCDD2 : #FF5252)))
plot(macd,   title = "MACD",   color = #2962FF)
plot(signal, title = "Signal", color = #FF6D00)
//plot(srsi_k, title = "StochRSI %K", color = #FF1100)
