//------ START Linear Regression Channel ------//

// indicator('Linear Regression Channel', overlay=true, max_bars_back = 1000, max_lines_count = 300)
lrc_src = input(defval=close, title='Source', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_len = input.int(defval=400, title='Length', minval=10, group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_devlen = input.float(defval=2., title='Deviation', minval=0.1, step=0.1, group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_extendit = input(defval=true, title='Extend Lines', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_showfibo = input(defval=false, title='Show Fibonacci Levels', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_showbroken = input.bool(defval=false, title='Show Broken Channel', inline='brk', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_brokencol = input.color(defval=#0fbcf9, title='', inline='brk', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_upcol = input.color(defval=#05c46b, title='Up/Down Trend Colors', inline='trcols', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_dncol = input.color(defval=#ff3f34, title='', inline='trcols', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_width = input(defval=2, title='Line Width', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")

var lrc_fibo_ratios = array.new_float(0)
var lrc_colors = array.new_color(2)
if barstate.isfirst
    array.unshift(lrc_colors, lrc_upcol)
    array.unshift(lrc_colors, lrc_dncol)
    array.push(lrc_fibo_ratios, 0.236)
    array.push(lrc_fibo_ratios, 0.382)
    array.push(lrc_fibo_ratios, 0.618)
    array.push(lrc_fibo_ratios, 0.786)

get_channel(lrc_src, lrc_len) =>
    lrc_mid = math.sum(lrc_src, lrc_len) / lrc_len
    lrc_slope = ta.linreg(lrc_src, lrc_len, 0) - ta.linreg(lrc_src, lrc_len, 1)
    lrc_intercept = lrc_mid - lrc_slope * math.floor(lrc_len / 2) + (1 - lrc_len % 2) / 2 * lrc_slope
    lrc_endy = lrc_intercept + lrc_slope * (lrc_len - 1)
    lrc_dev = 0.0
    for x = 0 to lrc_len - 1 by 1
        lrc_dev += math.pow(lrc_src[x] - (lrc_slope * (lrc_len - x) + lrc_intercept), 2)
        lrc_dev
    lrc_dev := math.sqrt(lrc_dev / lrc_len)
    [lrc_intercept, lrc_endy, lrc_dev, lrc_slope]

[y1_, y2_, lrc_dev, lrc_slope] = get_channel(lrc_src, lrc_len)

lrc_outofchannel = lrc_slope > 0 and close < y2_ - lrc_dev * lrc_devlen ? 0 : lrc_slope < 0 and close > y2_ + lrc_dev * lrc_devlen ? 2 : -1

var lrc_reglines = array.new_line(3)
var lrc_fibolines = array.new_line(4)

for x = 0 to 2 by 1
    if not lrc_showbroken or lrc_outofchannel != x or nz(lrc_outofchannel[1], -1) != -1
        line.delete(array.get(lrc_reglines, x))
    else
        line.set_color(array.get(lrc_reglines, x), color=lrc_brokencol)
        line.set_width(array.get(lrc_reglines, x), width=2)
        line.set_style(array.get(lrc_reglines, x), style=line.style_dotted)
        line.set_extend(array.get(lrc_reglines, x), extend=extend.none)

    array.set(lrc_reglines, x, line.new(x1=bar_index - (lrc_len - 1), y1=y1_ + lrc_dev * lrc_devlen * (x - 1), x2=bar_index, y2=y2_ + lrc_dev * lrc_devlen * (x - 1), color=array.get(lrc_colors, math.round(math.max(math.sign(lrc_slope), 0))), style=x % 2 == 1 ? line.style_solid : line.style_dashed, width=lrc_width, extend=lrc_extendit ? extend.right : extend.none, title="(LRC) Lines"))

if lrc_showfibo
    for x = 0 to 3 by 1
        line.delete(array.get(lrc_fibolines, x))
        array.set(lrc_fibolines, x, line.new(x1=bar_index - (lrc_len - 1), y1=y1_ - lrc_dev * lrc_devlen + lrc_dev * lrc_devlen * 2 * array.get(lrc_fibo_ratios, x), x2=bar_index, y2=y2_ - lrc_dev * lrc_devlen + lrc_dev * lrc_devlen * 2 * array.get(lrc_fibo_ratios, x), color=array.get(lrc_colors, math.round(math.max(math.sign(lrc_slope), 0))), style=line.style_dotted, width=lrc_width, extend=lrc_extendit ? extend.right : extend.none))

var label lrc_sidelab = label.new(x=bar_index - (lrc_len - 1), y=y1_, text='S', size=size.large, title="(LRC) Label")
lrc_txt = lrc_slope > 0 ? lrc_slope > lrc_slope[1] ? '⇑' : '⇗' : lrc_slope < 0 ? lrc_slope < lrc_slope[1] ? '⇓' : '⇘' : '⇒'
lrc_stl = lrc_slope > 0 ? lrc_slope > lrc_slope[1] ? label.style_label_up : label.style_label_upper_right : lrc_slope < 0 ? lrc_slope < lrc_slope[1] ? label.style_label_down : label.style_label_lower_right : label.style_label_right
label.set_style(lrc_sidelab, lrc_stl)
label.set_text(lrc_sidelab, lrc_txt)
label.set_x(lrc_sidelab, bar_index - (lrc_len - 1))
label.set_y(lrc_sidelab, lrc_slope > 0 ? y1_ - lrc_dev * lrc_devlen : lrc_slope < 0 ? y1_ + lrc_dev * lrc_devlen : y1_)
label.set_color(lrc_sidelab, lrc_slope > 0 ? lrc_upcol : lrc_slope < 0 ? lrc_dncol : color.blue)


// alertcondition(lrc_outofchannel, title='Channel Broken', message='Channel Broken')
// // direction
// lrc_trendisup = math.sign(lrc_slope) != math.sign(lrc_slope[1]) and lrc_slope > 0
// lrc_trendisdown = math.sign(lrc_slope) != math.sign(lrc_slope[1]) and lrc_slope < 0
// alertcondition(lrc_trendisup, title='Up trend', message='Up trend')
// alertcondition(lrc_trendisdown, title='Down trend', message='Down trend')

//------ END Linear Regression Channel ------//




