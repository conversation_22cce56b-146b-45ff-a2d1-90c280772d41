//@version=5
indicator("TEDnica Scalping", shorttitle='TEDnica Scalping', overlay=true, max_boxes_count = 500, max_bars_back = 5000)
// indicator("Adaptive Trend Finder (log)", shorttitle='Adaptive Trend Finder', overlay=true, max_bars_back=1200)

confidence(pearsonR) =>
    switch
        pearsonR < 0.2  => "Extremely Weak"
        pearsonR < 0.3  => "Very Weak"
        pearsonR < 0.4  => "Weak"
        pearsonR < 0.5  => "Mostly Weak"
        pearsonR < 0.6  => "Somewhat Weak"
        pearsonR < 0.7  => "Moderately Weak"
        pearsonR < 0.8  => "Moderate"
        pearsonR < 0.9  => "Moderately Strong"
        pearsonR < 0.92 => "Mostly Strong"
        pearsonR < 0.94 => "Strong"
        pearsonR < 0.96 => "Very Strong"
        pearsonR < 0.98 => "Exceptionally Strong"
        =>                        "Ultra Strong"

getTablePosition(string pos) =>
    switch pos
        "Bottom Right"  => position.bottom_right
        "Bottom Center" => position.bottom_center        
        "Bottom Left"   => position.bottom_left
        "Top Right"     => position.top_right
        "Top Left"      => position.top_left
        "Top Center"    => position.top_center
        "Middle Right"  => position.middle_right
        =>                 position.middle_left // "Middle Left" - default

// Calculate deviations for given length
calcDev(float source, int length) =>
    float logSource  = math.log(source)
    var int period_1 = length - 1
    if barstate.islast
        float sumX  = 0.0
        float sumXX = 0.0
        float sumYX = 0.0
        float sumY  = 0.0
        for int i=1 to length
            float lSrc = logSource[i-1]
            sumX  += i
            sumXX += i * i
            sumYX += i * lSrc
            sumY  +=     lSrc
        float slope     = nz((length * sumYX - sumX * sumY) / (length * sumXX - sumX * sumX))
        float average   = sumY / length
        float intercept = average - (slope * sumX / length) + slope
        float sumDev = 0.0
        float sumDxx = 0.0
        float sumDyy = 0.0
        float sumDyx = 0.0
        float regres = intercept + slope * period_1 * 0.5
        float sumSlp = intercept
        for int i=0 to period_1
            float lSrc = logSource[i]
            float dxt  =   lSrc - average
            float dyt  = sumSlp - regres
            lSrc   -= sumSlp
            sumSlp += slope
            sumDxx +=  dxt * dxt
            sumDyy +=  dyt * dyt
            sumDyx +=  dxt * dyt
            sumDev += lSrc * lSrc
        float unStdDev = math.sqrt(sumDev / period_1) // unbiased
        float divisor  =    sumDxx * sumDyy
        float pearsonR = nz(sumDyx / math.sqrt(divisor))
        [unStdDev, pearsonR, slope, intercept]
    else
        [na, na, na, na]

string t1 = "In Long-Term Channel mode, if the channel is not visible, scroll back on the chart for additional historical data. To view both Short-Term and Long-Term channels simultaneously, load this indicator twice on your chart."
string t2 = "Displays the length of the period automatically selected by the indicator that shows the strongest trend. This period is determined by identifying the highest correlation between price movements and trend direction."
string t3 = "Pearson's R is a statistical measure that evaluates the linear relationship between price movements and trend projection. A value closer to 1 indicates a strong positive correlation, increasing confidence in the trend direction based on historical data."
string t4 = "Displays the annualized return (CAGR) of the trend over the auto-selected period. This feature is available only for daily (D) and weekly (W) timeframes, providing insight into the expected yearly growth rate if the trend continues."

sourceInput = input.source(close, title="Source")

string group0 = "CHANNEL SETTINGS"
bool   periodMode       = input.bool  (         true, "Use Long-Term Channel", group=group0, tooltip=t1)
float  devMultiplier    = input.float (           2.0, "Deviation Multiplier:", group=group0, step=0.1)
color  colorInput       = input.color (  color.gray,             "", group=group0, inline=group0)
string lineStyle1       = input.string(       "Solid",             "", group=group0, inline=group0, options=["Solid", "Dotted", "Dashed"])
string extendStyle      = input.string("Extend Right",             "", group=group0, inline=group0, options=["Extend Right", "Extend Both", "Extend None", "Extend Left"])
int    fillTransparency = input.int   (            93, "Fill Transp:", group=group0, inline="mid", minval=0, maxval=100, step=1)
int channelTransparency = input.int   (            40, "Line Transp:", group=group0, inline="mid", minval=0, maxval=100, step=1)

string group1 = "MIDLINE SETTINGS"
color  colorInputMidline       = input.color ( color.blue, "", group=group1, inline=group1)
int    transpInput  = input.int   (     100,          "Transp:", group=group1, inline=group1, minval=0, maxval=100, step=10)
int    lineWidth    = input.int   (       1,      "Line Width:", group=group1, inline=group1)
string midLineStyle = input.string(               "Dashed",  "", group=group1, inline=group1, options=["Dotted", "Solid", "Dashed"])

string group2 = "TABLE SETTINGS"
bool showAutoSelectedPeriod =    input(true, "Show Auto-Selected Period", group=group2, tooltip=t2)
bool showTrendStrength =         input(true, "Show Trend Strength", group=group2, inline="secondLine")
bool showPearsonInput =          input.bool(false, "Show Pearson's R", group=group2, inline="secondLine", tooltip=t3)
bool showTrendAnnualizedReturn = input(true, "Show Trend Annualized Return", group=group2, tooltip=t4)
string tablePositionInput =      input.string("Bottom Right", "Table Position", options=["Bottom Right", "Bottom Left", "Middle Right", "Middle Left", "Top Right", "Top Left", "Top Center", "Bottom Center"], group=group2, inline="fourthLine")
string textSizeInput =           input.string(    "Normal", "Text Size", options=["Normal", "Large", "Small"], group=group2, inline="fourthLine")
        
// Helper function to get the multiplier based on timeframe
get_tf_multiplier() =>
    var float multiplier = 1.0
    if syminfo.type == "crypto"
        if timeframe.isdaily
            multiplier := 365 // ~365 trading days per year
        else if timeframe.isweekly
            multiplier := 52 // 52 weeks per year
        multiplier 
    else // Default for stocks and other asset types
        if timeframe.isdaily
            multiplier := 252 // ~252 trading days per year
        else if timeframe.isweekly
            multiplier := 52 // 52 weeks per year
        multiplier    

// Helper function to check if the timeframe is daily or weekly
is_valid_timeframe() =>
    timeframe.isdaily or timeframe.isweekly

var string EXTEND_STYLE = switch extendStyle
    "Extend Right" => extend.right
    "Extend Both"  => extend.both
    "Extend None"  => extend.none
    =>                extend.left

// Length Inputs
var array<int> Periods = periodMode ? array.from(na,300,350,400,450,500,550,600,650,700,750,800,850,900,950,1000,1050,1100,1150,1200) : array.from(na,20,30,40,50,60,70,80,90,100,110,120,130,140,150,160,170,180,190,200)

// Calculate deviations, correlation, slope, and intercepts for different lengths
[stdDev01, pearsonR01, slope01, intercept01] = calcDev(sourceInput, Periods.get( 1))
[stdDev02, pearsonR02, slope02, intercept02] = calcDev(sourceInput, Periods.get( 2))
[stdDev03, pearsonR03, slope03, intercept03] = calcDev(sourceInput, Periods.get( 3))
[stdDev04, pearsonR04, slope04, intercept04] = calcDev(sourceInput, Periods.get( 4))
[stdDev05, pearsonR05, slope05, intercept05] = calcDev(sourceInput, Periods.get( 5))
[stdDev06, pearsonR06, slope06, intercept06] = calcDev(sourceInput, Periods.get( 6))
[stdDev07, pearsonR07, slope07, intercept07] = calcDev(sourceInput, Periods.get( 7))
[stdDev08, pearsonR08, slope08, intercept08] = calcDev(sourceInput, Periods.get( 8))
[stdDev09, pearsonR09, slope09, intercept09] = calcDev(sourceInput, Periods.get( 9))
[stdDev10, pearsonR10, slope10, intercept10] = calcDev(sourceInput, Periods.get(10))
[stdDev11, pearsonR11, slope11, intercept11] = calcDev(sourceInput, Periods.get(11))
[stdDev12, pearsonR12, slope12, intercept12] = calcDev(sourceInput, Periods.get(12))
[stdDev13, pearsonR13, slope13, intercept13] = calcDev(sourceInput, Periods.get(13))
[stdDev14, pearsonR14, slope14, intercept14] = calcDev(sourceInput, Periods.get(14))
[stdDev15, pearsonR15, slope15, intercept15] = calcDev(sourceInput, Periods.get(15))
[stdDev16, pearsonR16, slope16, intercept16] = calcDev(sourceInput, Periods.get(16))
[stdDev17, pearsonR17, slope17, intercept17] = calcDev(sourceInput, Periods.get(17))
[stdDev18, pearsonR18, slope18, intercept18] = calcDev(sourceInput, Periods.get(18))
[stdDev19, pearsonR19, slope19, intercept19] = calcDev(sourceInput, Periods.get(19))

if barstate.islast
    // Find the highest Pearson's R
    float highestPearsonR = math.max(pearsonR01, pearsonR02, pearsonR03, pearsonR04, pearsonR05, pearsonR06, pearsonR07, pearsonR08, pearsonR09, pearsonR10, pearsonR11, pearsonR12, pearsonR13, pearsonR14, pearsonR15, pearsonR16, pearsonR17, pearsonR18, pearsonR19)

    // Determine selected length, slope, intercept, and deviations
    int   detectedPeriod  = na
    float detectedSlope   = na
    float detectedIntrcpt = na
    float detectedStdDev  = na

    switch highestPearsonR
        pearsonR01 =>
            detectedPeriod  := Periods.get(1)
            detectedSlope   :=     slope01
            detectedIntrcpt := intercept01
            detectedStdDev  :=    stdDev01
        pearsonR02 =>
            detectedPeriod  := Periods.get(2)
            detectedSlope   :=     slope02
            detectedIntrcpt := intercept02
            detectedStdDev  :=    stdDev02
        pearsonR03 =>
            detectedPeriod  := Periods.get(3)
            detectedSlope   :=     slope03
            detectedIntrcpt := intercept03
            detectedStdDev  :=    stdDev03
        pearsonR04 =>
            detectedPeriod  := Periods.get(4)
            detectedSlope   :=     slope04
            detectedIntrcpt := intercept04
            detectedStdDev  :=    stdDev04
        pearsonR05 =>
            detectedPeriod  := Periods.get(5)
            detectedSlope   :=     slope05
            detectedIntrcpt := intercept05
            detectedStdDev  :=    stdDev05
        pearsonR06 =>
            detectedPeriod  := Periods.get(6)
            detectedSlope   :=     slope06
            detectedIntrcpt := intercept06
            detectedStdDev  :=    stdDev06
        pearsonR07 =>
            detectedPeriod  := Periods.get(7)
            detectedSlope   :=     slope07
            detectedIntrcpt := intercept07
            detectedStdDev  :=    stdDev07
        pearsonR08 =>
            detectedPeriod  := Periods.get(8)
            detectedSlope   :=     slope08
            detectedIntrcpt := intercept08
            detectedStdDev  :=    stdDev08
        pearsonR09 =>
            detectedPeriod  := Periods.get(9)
            detectedSlope   :=     slope09
            detectedIntrcpt := intercept09
            detectedStdDev  :=    stdDev09
        pearsonR10 => 
            detectedPeriod  := Periods.get(10)
            detectedSlope   :=     slope10
            detectedIntrcpt := intercept10
            detectedStdDev  :=    stdDev10
        pearsonR11 =>        
            detectedPeriod  := Periods.get(11)
            detectedSlope   :=     slope11
            detectedIntrcpt := intercept11
            detectedStdDev  :=    stdDev11
        pearsonR12 =>
            detectedPeriod  := Periods.get(12)
            detectedSlope   :=     slope12
            detectedIntrcpt := intercept12
            detectedStdDev  :=    stdDev12
        pearsonR13 =>
            detectedPeriod  := Periods.get(13)
            detectedSlope   :=     slope13
            detectedIntrcpt := intercept13
            detectedStdDev  :=    stdDev13
        pearsonR14 =>
            detectedPeriod  := Periods.get(14)
            detectedSlope   :=     slope14
            detectedIntrcpt := intercept14
            detectedStdDev  :=    stdDev14
        pearsonR15 =>
            detectedPeriod  := Periods.get(15)
            detectedSlope   :=     slope15
            detectedIntrcpt := intercept15
            detectedStdDev  :=    stdDev15
        pearsonR16 =>
            detectedPeriod  := Periods.get(16)
            detectedSlope   :=     slope16
            detectedIntrcpt := intercept16
            detectedStdDev  :=    stdDev16
        pearsonR17 =>
            detectedPeriod  := Periods.get(17)
            detectedSlope   :=     slope17
            detectedIntrcpt := intercept17
            detectedStdDev  :=    stdDev17
        pearsonR18 =>
            detectedPeriod  := Periods.get(18)
            detectedSlope   :=     slope18
            detectedIntrcpt := intercept18
            detectedStdDev  :=    stdDev18
        => // pearsonR19
            detectedPeriod  := Periods.get(19)
            detectedSlope   :=     slope19
            detectedIntrcpt := intercept19
            detectedStdDev  :=    stdDev19

    var line upperLine = na,   var linefill upperFill = na
    var line  baseLine = na
    var line lowerLine = na,   var linefill lowerFill = na

    // Calculate start and end price based on detected slope and intercept
    float startPrice = math.exp(detectedIntrcpt + detectedSlope * (detectedPeriod - 1))
    float   endPrice = math.exp(detectedIntrcpt)
    
    int         startAtBar = bar_index - detectedPeriod + 1
    var color ChannelColor = color.new(colorInput, channelTransparency)

    if na(baseLine)
        baseLine := line.new(startAtBar, startPrice, bar_index, endPrice,
                             width=lineWidth, extend=EXTEND_STYLE,
                             color=color.new(colorInputMidline, transpInput),
                             style=midLineStyle == "Dotted" ? line.style_dotted :
                                   midLineStyle == "Dashed" ? line.style_dashed : line.style_solid)
    else
        line.set_xy1(baseLine, startAtBar, startPrice)
        line.set_xy2(baseLine,  bar_index,   endPrice)

    float upperStartPrice = startPrice * math.exp(devMultiplier * detectedStdDev)
    float upperEndPrice   =   endPrice * math.exp(devMultiplier * detectedStdDev)
    if na(upperLine)
        upperLine := line.new(startAtBar, upperStartPrice, bar_index, upperEndPrice,
                             width=1, extend=EXTEND_STYLE,
                             color=ChannelColor,
                             style=lineStyle1 == "Dotted" ? line.style_dotted :
                                   lineStyle1 == "Dashed" ? line.style_dashed : line.style_solid)
    else
        line.set_xy1  (upperLine, startAtBar, upperStartPrice)
        line.set_xy2  (upperLine,  bar_index,   upperEndPrice)
        line.set_color(upperLine, colorInput)

    float lowerStartPrice = startPrice / math.exp(devMultiplier * detectedStdDev)
    float   lowerEndPrice =   endPrice / math.exp(devMultiplier * detectedStdDev)
    if na(lowerLine)
        lowerLine := line.new(startAtBar, lowerStartPrice, bar_index, lowerEndPrice,
                             width=1, extend=EXTEND_STYLE,
                             color=ChannelColor,
                             style=lineStyle1 == "Dotted" ? line.style_dotted :
                                   lineStyle1 == "Dashed" ? line.style_dashed : line.style_solid)
    else
        line.set_xy1  (lowerLine, startAtBar, lowerStartPrice)
        line.set_xy2  (lowerLine,  bar_index,   lowerEndPrice)
        line.set_color(lowerLine, colorInput)

    if na(upperFill)
        upperFill := linefill.new(upperLine, baseLine, color=color.new(colorInput, fillTransparency))
    if na(lowerFill)
        lowerFill := linefill.new(baseLine, lowerLine, color=color.new(colorInput, fillTransparency))

    var table t = na
    if periodMode
        t := table.new(position.bottom_center, 2, 3)
    else
        t := table.new(getTablePosition(tablePositionInput), 2, 3)

    string text1 = periodMode ? "Auto-Selected Period (Long Term): " + str.tostring(detectedPeriod) : "Auto-Selected Period: " + str.tostring(detectedPeriod)
    var colorInputLight = color.new(colorInput, 0)

    // Display or hide the "Auto-Selected Period" cell
    if showAutoSelectedPeriod
        table.cell(t, 0, 0, text1, text_color=colorInputLight, text_size=textSizeInput == "Large" ? size.large : textSizeInput == "Small" ? size.small : size.normal)

    // Display or hide the "Trend Strength" or "Pearson's R" cell
    if showTrendStrength
        if showPearsonInput
            table.cell(t, 0, 1, "Pearson's R: " + str.tostring(detectedSlope > 0.0 ? -highestPearsonR : highestPearsonR, "#.###"), text_color=colorInput, text_size=textSizeInput == "Large" ? size.large : textSizeInput == "Small" ? size.small : size.normal)
        else
            table.cell(t, 0, 1, "Trend Strength: " + confidence(highestPearsonR), text_color=colorInput, text_size=textSizeInput == "Large" ? size.large : textSizeInput == "Small" ? size.small : size.normal)

    // Calculate CAGR
    float cagr = na
    if not na(detectedPeriod) and bar_index >= detectedPeriod and is_valid_timeframe()
        float num_of_periods = detectedPeriod
        float multiplier = get_tf_multiplier()
        float startClosePrice = close[detectedPeriod - 1]
        cagr := math.pow(close / startClosePrice, multiplier / num_of_periods) - 1

    // Display or hide the "Trend Annualized Return" cell
    if showTrendAnnualizedReturn and is_valid_timeframe()
        table.cell(t, 0, 2, "Trend Annualized Return: " + (not na(cagr) ? str.tostring(cagr * 100, "#.#") + "%" : "N/A"), text_color=colorInput, text_size=textSizeInput == "Large" ? size.large : textSizeInput == "Small" ? size.small : size.normal)













//@version=5
//indicator("Support and Resistance (High Volume Boxes) [ChartPrime]", shorttitle = "SR Breaks and Retests [ChartPrime]", overlay=true, max_boxes_count = 50)


// ---------------------------------------------------------------------------------------------------------------------}
// 𝙐𝙎𝙀𝙍 𝙄𝙉𝙋𝙐𝙏𝙎
// ---------------------------------------------------------------------------------------------------------------------{
int   lookbackPeriod = input.int(20, "Lookback Period", minval=1, group = "Settings")
int   vol_len        = input.int(2, "Delta Volume Filter Length", tooltip = "Higher input, will filter low volume boxes"
                                                                                                   , group = "Settings")
float box_withd      = input.float(1, "Adjust Box Width", maxval = 1000, minval = 0, step = 0.1)


// ---------------------------------------------------------------------------------------------------------------------}
// 𝙄𝙉𝘿𝙄𝘾𝘼𝙏𝙊𝙍 𝘾𝘼𝙇𝘾𝙐𝙇𝘼𝙏𝙄𝙊𝙉𝙎
// ---------------------------------------------------------------------------------------------------------------------{
// Delta Volume Function
upAndDownVolume() =>
    posVol = 0.0
    negVol = 0.0
    
    var isBuyVolume = true    

    switch
        close > open   => isBuyVolume := true
        close < open   => isBuyVolume := false

    if isBuyVolume
        posVol += volume
    else
        negVol -= volume

    posVol + negVol


// Function to identify support and resistance boxes
calcSupportResistance(src, lookbackPeriod) =>
    // Volume
    Vol    = upAndDownVolume()
    vol_hi = ta.highest(Vol/2.5, vol_len)
    vol_lo = ta.lowest(Vol/2.5, vol_len)

    var float supportLevel      = na
    var float supportLevel_1    = na    
    var float resistanceLevel   = na
    var float resistanceLevel_1 = na    
    var box   sup               = na
    var box   res               = na
    var color res_color         = na
    var color sup_color         = na    
    var float multi             = na

    var bool  brekout_res       = na
    var bool  brekout_sup       = na
    var bool  res_holds         = na
    var bool  sup_holds         = na

    // Find pivot points
    pivotHigh = ta.pivothigh(src, lookbackPeriod, lookbackPeriod)
    pivotLow  = ta.pivotlow (src, lookbackPeriod, lookbackPeriod)
    // Box width
    atr       = ta.atr(200)
    withd     = atr * box_withd

    // Find support levels with Positive Volume
    if (not na(pivotLow)) and Vol > vol_hi 

        supportLevel   := pivotLow
        supportLevel_1 := supportLevel-withd       

        topLeft         = chart.point.from_index(bar_index-lookbackPeriod, supportLevel)
        bottomRight     = chart.point.from_index(bar_index, supportLevel_1)

        sup_color      := color.from_gradient(Vol, 0, ta.highest(Vol, 25), color(na), color.new(color.green, 30))

        sup := box.new(
                       top_left     = topLeft, 
                       bottom_right = bottomRight, 
                       border_color = color.green, 
                       border_width = 1, 
                       bgcolor      = sup_color, 
                       text         = "Vol: "+str.tostring(math.round(Vol,2)), 
                       text_color   = chart.fg_color, 
                       text_size    = size.small
                       ) 


    // Find resistance levels with Negative Volume
    if (not na(pivotHigh)) and Vol < vol_lo
   
        resistanceLevel   := pivotHigh
        resistanceLevel_1 := resistanceLevel+withd

        topLeft            = chart.point.from_index(bar_index-lookbackPeriod, resistanceLevel)
        bottomRight        = chart.point.from_index(bar_index, resistanceLevel_1)

        res_color         := color.from_gradient(Vol, ta.lowest(Vol, 25), 0, color.new(color.red, 30), color(na))

        res := box.new(
                       top_left     = topLeft, 
                       bottom_right = bottomRight, 
                       border_color = color.red, 
                       border_width = 1, 
                       bgcolor      = res_color, 
                       text         = "Vol: "+str.tostring(math.round(Vol,2)), 
                       text_color   = chart.fg_color, 
                       text_size    = size.small
                       ) 

    // Adaptive Box Len
    sup.set_right(bar_index+1)
    res.set_right(bar_index+1)

    // Break of support or resistance conditions
    brekout_res := ta.crossover(low, resistanceLevel_1)
    res_holds   := ta.crossunder(high, resistanceLevel)

    sup_holds   := ta.crossover(low, supportLevel)
    brekout_sup := ta.crossunder(high, supportLevel_1)

    // Change Color of Support to red if it was break, change color of resistance to green if it was break
    if brekout_sup
        sup.set_bgcolor(color.new(color.red, 80))
        sup.set_border_color(color.red)
        sup.set_border_style(line.style_dashed)

    if sup_holds
        sup.set_bgcolor(sup_color)
        sup.set_border_color(color.green)
        sup.set_border_style(line.style_solid)

    if brekout_res
        res.set_bgcolor(color.new(color.green, 80))
        res.set_border_color(color.new(color.green, 0))
        res.set_border_style(line.style_dashed)

    if res_holds
        res.set_bgcolor(res_color)
        res.set_border_color(color.new(color.red, 0))
        res.set_border_style(line.style_solid)

    [supportLevel, resistanceLevel, brekout_res, res_holds, sup_holds, brekout_sup]


// Calculate support and resistance levels and their breakouts
[supportLevel,
         resistanceLevel, 
             brekout_res, 
                 res_holds, 
                     sup_holds, 
                         brekout_sup] = calcSupportResistance(close, lookbackPeriod)


// Check if Resistance become Support or Support Become Resistance
var bool res_is_sup = na
var bool sup_is_res = na

switch
    brekout_res => res_is_sup := true
    res_holds   => res_is_sup := false

switch
    brekout_sup => sup_is_res := true
    sup_holds   => sup_is_res := false


// ---------------------------------------------------------------------------------------------------------------------}
// 𝙑𝙄𝙎𝙐𝘼𝙇𝙄𝙕𝘼𝙏𝙄𝙊𝙉
// ---------------------------------------------------------------------------------------------------------------------{
// Plot Res and Sup breakouts and holds 
plotchar(res_holds, "Resistance Holds", "◆", 
             color = #e92929,   size = size.tiny, location = location.abovebar, offset = -1)
plotchar(sup_holds, "Support Holds",    "◆", 
             color = #20ca26, size = size.tiny, location = location.belowbar, offset = -1)

plotchar(brekout_res and res_is_sup[1], "Resistance as Support Holds", "◆", 
             color = #20ca26, size = size.tiny, location = location.belowbar, offset = -1)
plotchar(brekout_sup and sup_is_res[1], "Support as Resistance Holds", "◆", 
             color = #e92929,   size = size.tiny, location = location.abovebar, offset = -1)

// Break Out Labels
if brekout_sup and not sup_is_res[1]
    label.new(
              bar_index[1], supportLevel[1], 
              text       = "Break Sup", 
              style      = label.style_label_down, 
              color      = #7e1e1e, 
              textcolor  = chart.fg_color, 
              size       = size.small
              )

if brekout_res and not res_is_sup[1]
    label.new(
              bar_index[1], resistanceLevel[1], 
              text       = "Break Res", 
              style      = label.style_label_up, 
              color      = #2b6d2d, 
              textcolor  = chart.fg_color, 
              size       = size.small
              )


// ◆
// ---------------------------------------------------------------------------------------------------------------------}












//@version=5

// indicator("Volume Profile with Node Detection [LuxAlgo]", "LuxAlgo - Volume Profile with Node Detection", overlay = true, max_boxes_count = 500, max_bars_back = 5000)

//---------------------------------------------------------------------------------------------------------------------
// Settings
//---------------------------------------------------------------------------------------------------------------------{

display = display.all - display.status_line

vn_volumeNodesGroup = 'Volume Nodes'

vn_peakTTip = 'A volume peak node is recognized when the volume profile nodes for the N preceding and N succeeding nodes are lower than that of the evaluated one, where N is determined by the \'Node Detection Percent %\' option'
vn_peaksShow = input.string('Peaks', 'Volume Peaks', options = ['Peaks', 'Clusters', 'None'], inline = 'vnP', tooltip = vn_peakTTip, group = vn_volumeNodesGroup, display = display)
vn_peakVolumeColor = input.color(color.new(color.blue, 50), '', inline = 'vnP', group = vn_volumeNodesGroup)
vn_peaksNumberOfNodes = input.int(9, '  Node Detection Percent %', minval = 0, maxval = 100, group = vn_volumeNodesGroup, display = display) / 100
vn_peaksShow := vn_peaksNumberOfNodes == 0 ? 'None' : vn_peaksShow

vn_troughsTTip  = 'A volume trough node is recognized when the volume profile nodes for the N preceding and N succeeding nodes exceed that of the evaluated one, where N is determined by the \'Node Detection Percent %\' option'
vn_troughsShow = input.string('None', 'Volume Troughs', options = ['Troughs', 'Clusters', 'None'], inline = 'vnT', tooltip = vn_troughsTTip, group = vn_volumeNodesGroup, display = display)
vn_troughVolumeColor = input.color(color.new(color.gray, 50), '', inline = 'vnT', group = vn_volumeNodesGroup)
vn_troughsNumberOfNodes = input.int(7, '  Node Detection Percent %', minval = 0, maxval = 100, group = vn_volumeNodesGroup, display = display) / 100
vn_troughsShow := vn_troughsNumberOfNodes == 0 ? 'None' : vn_troughsShow

vn_thresholdTTip = 'A threshold value specified as a percentage is utilized to detect peak/trough volume nodes. If a value is set, the detection will disregard volume node values lower than the specified threshold.'
vn_VolumeNodeThreshold = input.int(1, 'Volume Node Threshold %', minval = 0, maxval = 100, tooltip = vn_thresholdTTip, group = vn_volumeNodesGroup, display = display) / 100

vn_highestNVolumeNodes = input.int(0, 'Highest Volume Nodes', minval = 0, maxval = 31, inline = 'vnL', group = vn_volumeNodesGroup, display = display)
vn_highestVolumeColor = input.color(color.new(color.orange, 25), '', inline = 'vnL', group = vn_volumeNodesGroup)

vn_lowestNVolumeNodes = input.int(0, 'Lowest Volume Nodes', minval = 0, maxval = 31, inline = 'vnH', group = vn_volumeNodesGroup, display = display)
vn_lowestVolumeColor = input.color(color.new(color.navy, 25), '', inline = 'vnH', group = vn_volumeNodesGroup)

vp_componentsGroup = 'Volume Profile - Components'

vp_profileShow = input.bool(true, 'Volume Profile', inline = 'vp', group = vp_componentsGroup)
vp_profileGradientColors = input.string('Gradient Colors', '', options = ['Gradient Colors', 'Classic Colors' ], inline = 'vp', group = vp_componentsGroup)
vp_valueAreaUpColor = input.color(color.new(#2962ff, 30), '  Value Area Up / Down', inline = 'VA', group = vp_componentsGroup)
vp_valueAreaDwonColor = input.color(color.new(#fbc02d, 30), '/', inline = 'VA', group = vp_componentsGroup)
vp_profileUpVolumeColor = input.color(color.new(#5d606b, 50), '  Profile Up / Down Volume', inline = 'VP', group = vp_componentsGroup)
vp_profileDownVolumeColor = input.color(color.new(#d1d4dc, 50), '/', inline = 'VP', group = vp_componentsGroup)

vp_pocShow = input.string('None', 'Point of Control', options = ['Developing', 'Regular', 'None'], inline = 'poc', group = vp_componentsGroup, display = display)
vp_pocColor = input.color(#fbc02d, '', inline = 'poc', group = vp_componentsGroup)
vp_pocWidth = input.int(2, 'Width', inline = 'poc', group = vp_componentsGroup, display = display)

vp_vahShow = input.bool(false, 'Value Area High (VAH)', inline = 'vah', group = vp_componentsGroup)
vp_vahColor = input.color(#2962ff, '', inline = 'vah', group = vp_componentsGroup)

vp_valShow = input.bool(false, 'Value Area Low (VAL)', inline = 'val', group = vp_componentsGroup)
vp_valColor = input.color(#2962ff, '', inline = 'val', group = vp_componentsGroup)

vp_profileLevels = input.string('Small', "Profile Price Labels", options=['Tiny', 'Small', 'Normal', 'None'], group = vp_componentsGroup, display = display)

vp_displayGroup = 'Volume Profile - Display Settings'

vp_profileLength = input.int(400, 'Profile Lookback Length', minval = 10, maxval = 5000, step = 10, group = vp_displayGroup, display = display)
vp_profileLength:= last_bar_index < vp_profileLength ? last_bar_index : vp_profileLength - 1

vp_valueAreaThreshold = input.float(70, 'Value Area (%)', minval = 0, maxval = 100, group = vp_displayGroup, display = display) / 100

vp_profilePlracment = input.string('Right', 'Profile Placement', options = ['Right', 'Left'], group = vp_displayGroup, display = display), profilePlacementRight = vp_profilePlracment == 'Right' 
vp_profileNumberOfRows = input.int(100, 'Profile Number of Rows' , minval = 30, maxval = 130 , step = 10, group = vp_displayGroup, display = display)
vp_profileWidth = input.float(31, 'Profile Width', minval = 0, maxval = 250, group = vp_displayGroup, display = display) / 100
vp_profileHorizontalOffset = input.int(13, 'Profile Horizontal Offset', maxval = 50, group = vp_displayGroup, display = display)

vp_valueAreaBackground = input.bool(false, 'Value Area Background  ', inline = 'vBG', group = vp_displayGroup)
vp_valueAreaBackgroundColor = input.color(color.new(#2962ff, 89), '', inline = 'vBG', group = vp_displayGroup)

vp_profileBackground = input.bool(false, 'Profile Range Background ', inline = 'pBG', group = vp_displayGroup)
vp_profileBackgroundColor  = input.color(color.new(#2962ff, 95), '', inline = 'pBG', group = vp_displayGroup)

//---------------------------------------------------------------------------------------------------------------------}
// User Defined Types
//---------------------------------------------------------------------------------------------------------------------{

type BAR
    float open   = open
    float high   = high
    float low    = low
    float close  = close
    float volume = volume
    int   index  = bar_index

type barData
    float [] barHigh
    float [] barLow
    float [] barVolume
    bool  [] barPolarity
    int   [] barCount

type volumeData
    float [] totalVolume
    float [] bullishVolume
    float [] bearishVolume
    int   [] endProfileIndex
    bool  [] peakVolume
    bool  [] troughVolume

type volumeProfile
    box         []  boxes
    chart.point []  pocPoints
    polyline        pocPolyline
    int             pocLevel
    int             vahLevel
    int             valLevel
    int             startIndex

//---------------------------------------------------------------------------------------------------------------------}
// Variables
//---------------------------------------------------------------------------------------------------------------------{

BAR bar = BAR.new()
BAR [] ltfBarData = array.new<BAR> (1, BAR.new())

var barData barDataArray = barData.new(
     array.new <float> (na), 
     array.new <float> (na), 
     array.new <float> (na), 
     array.new <bool>  (na), 
     array.new <int>   (na)
 )

volumeData volumeDataArray = volumeData.new(
     array.new <float> (vp_profileNumberOfRows, 0.), 
     array.new <float> (vp_profileNumberOfRows, 0.), 
     array.new <float> (vp_profileNumberOfRows, 0.),
     array.new <int>   (vp_profileNumberOfRows, 0 ),
     array.new <bool>  (vp_profileNumberOfRows, 0.),
     array.new <bool>  (vp_profileNumberOfRows, 0.)
 )

var volumeProfile VP = volumeProfile.new(
     array.new<box>         (na),
     array.new<chart.point> (na),
     polyline.new           (na), na, na, na, na
 )

var float highestPrice = na
var float lowestPrice = na

//---------------------------------------------------------------------------------------------------------------------}
// Functions / Methods
//---------------------------------------------------------------------------------------------------------------------{

renderLine(_x1, _y1, _x2, _y2, _xloc, _extend, _color, _style, _width) =>
    var id = line.new(_x1, _y1, _x2, _y2, _xloc, _extend, _color, _style, _width)
    line.set_xy1(id, _x1, _y1)
    line.set_xy2(id, _x2, _y2)
    line.set_color(id, _color)

renderLabel(_x, _y, _text, _color, _style, _textcolor, _size, _tooltip) =>
    var lb = label.new(_x, _y, _text, xloc.bar_index, yloc.price, _color, _style, _textcolor, _size, text.align_left, _tooltip)
    lb.set_xy(_x, _y)
    lb.set_text(_text)
    lb.set_tooltip(_tooltip)
    lb.set_textcolor(_textcolor)

requestBarData(_lowerTimeframe) => request.security_lower_tf(syminfo.tickerid, _lowerTimeframe, BAR.new(), ignore_invalid_timeframe = true)

calculateTimeframe(_depth) => 
    int tfInMs = timeframe.in_seconds(timeframe.period)
    int  mInMS = 60

    if _depth == 2
        switch
            tfInMs <                 30  =>  '1S'
            tfInMs <          1 * mInMS  =>  '5S'

            tfInMs <=        15 * mInMS  =>   '1'
            tfInMs <=        60 * mInMS  =>   '5'
            tfInMs <=       240 * mInMS  =>  '15'
            tfInMs <=      1440 * mInMS  =>  '60'
            => 'D'

    else if _depth == 1
        switch
            tfInMs <                 15  =>  '1S'
            tfInMs <                 30  =>  '5S'
            tfInMs <          1 * mInMS  => '15S'

            tfInMs <=         5 * mInMS  =>   '1'
            tfInMs <=        15 * mInMS  =>   '5'
            tfInMs <=        60 * mInMS  =>  '15'
            tfInMs <=       240 * mInMS  =>  '60'
            tfInMs <=      1440 * mInMS  => '240'
            => 'D'

getTextSize(_text) =>
    if _text != 'None'
        switch _text
            'Tiny'   => size.tiny
            'Small'  => size.small 
            'Normal' => size.normal
            => size.auto

//---------------------------------------------------------------------------------------------------------------------}
// Calculations - Volume Profile
//---------------------------------------------------------------------------------------------------------------------{

profileLevesSize  = getTextSize(vp_profileLevels)

if bar.index == last_bar_index - vp_profileLength
    VP.startIndex := bar.index
    lowestPrice := bar.low 
    highestPrice := bar.high
else if bar.index > last_bar_index - vp_profileLength
    lowestPrice := math.min(bar.low, lowestPrice)
    highestPrice := math.max(bar.high, highestPrice)

//if vp_profileLength <= 200
//    ltfBarData := requestBarData(calculateTimeframe(2))  
//else 
if vp_profileLength <= 700
    ltfBarData := requestBarData(calculateTimeframe(2)) 
else
    ltfBarData := array.new<BAR> (1, BAR.new(bar.open, bar.high, bar.low, bar.close, bar.volume))

if barstate.ishistory and (bar.index >= last_bar_index - vp_profileLength) and bar.index < last_bar_index and ltfBarData.size() > 0

    log.info("yaz_kizim {0} {1}", ltfBarData.get(0).volume, na(nz(ltfBarData.get(0).volume)) )

    if ltfBarData.size() > 0 and not na(nz(ltfBarData.get(0).volume))
        for currentLtfBar = 0 to ltfBarData.size() - 1
            barDataArray.barHigh.push(ltfBarData.get(currentLtfBar).high)
            barDataArray.barLow.push(ltfBarData.get(currentLtfBar).low)
            barDataArray.barVolume.push(ltfBarData.get(currentLtfBar).volume)
            barDataArray.barPolarity.push(ltfBarData.get(currentLtfBar).close > ltfBarData.get(currentLtfBar).open)

        barDataArray.barCount.push(ltfBarData.size())

priceStep = (highestPrice - lowestPrice) / vp_profileNumberOfRows

if barstate.islast and ltfBarData.size() > 0 //  barDataArray.barVolume.size() > 0 //

    if VP.boxes.size() > 0
        for boxIndex = 0 to VP.boxes.size() - 1
            box.delete(VP.boxes.shift())

    if barDataArray.barCount.size() > vp_profileLength
        barCount = barDataArray.barCount.shift()
        for barCountIndex = 0 to barCount - 1
            barDataArray.barHigh.shift()
            barDataArray.barLow.shift()
            barDataArray.barVolume.shift()
            barDataArray.barPolarity.shift()

    VP.pocPoints.clear()
    VP.pocPolyline.delete()

    if ltfBarData.size() > 0 and not na(nz(ltfBarData.get(0).volume))
        for currentLtfBar = 0 to ltfBarData.size() - 1
            barDataArray.barHigh.push(ltfBarData.get(currentLtfBar).high)
            barDataArray.barLow.push(ltfBarData.get(currentLtfBar).low)
            barDataArray.barVolume.push(ltfBarData.get(currentLtfBar).volume)
            barDataArray.barPolarity.push(ltfBarData.get(currentLtfBar).close > ltfBarData.get(currentLtfBar).open)

        barDataArray.barCount.push(ltfBarData.size())

    barIndex = vp_profileLength
    numberOfBars = 0
    arraySize = barDataArray.barVolume.size()

    for arrayIndex = 0 to arraySize - 1

        levelHigh = barDataArray.barHigh.get(arrayIndex)
        levelLow = barDataArray.barLow.get(arrayIndex)
        levelVolume = barDataArray.barVolume.get(arrayIndex)

        // Shoutout to @tkarolak for contributing to the code's optimization! Much appreciated.
        
        int startSlotIndex = math.max(math.floor((levelLow - lowestPrice) / priceStep), 0)
        int endSlotIndex = math.min(math.floor((levelHigh - lowestPrice) / priceStep), vp_profileNumberOfRows - 1)
        
        for priceLevelIndex = startSlotIndex to endSlotIndex

            float priceLevel = lowestPrice + priceLevelIndex * priceStep

            volumeProportion = switch
                levelLow >= priceLevel and levelHigh > priceLevel + priceStep => (priceLevel + priceStep - levelLow) / (levelHigh - levelLow)
                levelHigh <= priceLevel + priceStep and levelLow < priceLevel => (levelHigh - priceLevel) / (levelHigh - levelLow)
                levelLow >= priceLevel and levelHigh <= priceLevel + priceStep => 1
                => priceStep / (levelHigh - levelLow)

            volumeDataArray.totalVolume.set(priceLevelIndex, volumeDataArray.totalVolume.get(priceLevelIndex) + levelVolume * volumeProportion)

            if barDataArray.barPolarity.get(arrayIndex)
                volumeDataArray.bullishVolume.set(priceLevelIndex, volumeDataArray.bullishVolume.get(priceLevelIndex) + levelVolume * volumeProportion)

//        priceLevelIndex = 0
//        for priceLevel = lowestPrice to highestPrice - priceStep by priceStep
//
//            if levelHigh >= priceLevel and levelLow < priceLevel + priceStep
//
//                volumeProportion = if levelLow >= priceLevel and levelHigh > priceLevel + priceStep
//                    (priceLevel + priceStep - levelLow) / (levelHigh - levelLow)
//                else if levelHigh <= priceLevel + priceStep and levelLow < priceLevel
//                    (levelHigh - priceLevel) / (levelHigh - levelLow)
//                else if levelLow >= priceLevel and levelHigh <= priceLevel + priceStep
//                    1
//                else
//                    priceStep / (levelHigh - levelLow)
//
//                volumeDataArray.totalVolume.set(priceLevelIndex, volumeDataArray.totalVolume.get(priceLevelIndex) + levelVolume * volumeProportion)
//
//                if barDataArray.barPolarity.get(arrayIndex)
//                    volumeDataArray.bullishVolume.set(priceLevelIndex, volumeDataArray.bullishVolume.get(priceLevelIndex) + levelVolume * volumeProportion)
//            priceLevelIndex += 1

        if vp_pocShow == 'Developing'
            if arrayIndex == barDataArray.barCount.get(vp_profileLength - barIndex)
                VP.pocPoints.push(chart.point.from_index(bar.index[barIndex], math.avg(bar.high[barIndex], bar.low[barIndex])))
                VP.pocPoints.push(chart.point.from_index(bar.index[barIndex] + 1, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max()) + .5) * priceStep))
                numberOfBars += barDataArray.barCount.get(vp_profileLength - barIndex)
                barIndex  -= 1
            else if arrayIndex == (numberOfBars + barDataArray.barCount.get(vp_profileLength - barIndex)) and numberOfBars != 0
                VP.pocPoints.push(chart.point.from_index(bar.index[barIndex] + 1, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max()) + .5) * priceStep))
                numberOfBars += barDataArray.barCount.get(vp_profileLength - barIndex)
                barIndex  -= 1
            else if barIndex == 0
                VP.pocPoints.push(chart.point.from_index(bar.index[barIndex] + 1, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max()) + .5) * priceStep))
                numberOfBars += barDataArray.barCount.get(vp_profileLength - barIndex)

    VP.pocPolyline := polyline.new(VP.pocPoints, false, false, xloc.bar_index, vp_pocColor, color(na), line.style_solid, vp_pocWidth)

    for volumeIndex = 0 to vp_profileNumberOfRows - 1
        bearishVolume = 2 * volumeDataArray.bullishVolume.get(volumeIndex) - volumeDataArray.totalVolume.get(volumeIndex)
        volumeDataArray.bearishVolume.set(volumeIndex, volumeDataArray.bearishVolume.get(volumeIndex) + bearishVolume * (bearishVolume > 0 ? 1 : -1) )

    VP.pocLevel := volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max())
    totalTradedVolume = volumeDataArray.totalVolume.sum() * vp_valueAreaThreshold
    valueAreaVolume = VP.pocLevel != -1 ? volumeDataArray.totalVolume.get(VP.pocLevel) : 0
    VP.vahLevel := VP.pocLevel
    VP.valLevel := VP.pocLevel
    
    while valueAreaVolume < totalTradedVolume
        if VP.valLevel == 0 and VP.vahLevel == vp_profileNumberOfRows - 1
            break

        volumeAbovePOC = 0.
        if VP.vahLevel < vp_profileNumberOfRows - 1 
            volumeAbovePOC := volumeDataArray.totalVolume.get(VP.vahLevel + 1)

        volumeBelowPOC = 0.
        if VP.valLevel > 0
            volumeBelowPOC := volumeDataArray.totalVolume.get(VP.valLevel - 1)
        
        if volumeBelowPOC == 0 and volumeAbovePOC == 0
            break

        if volumeAbovePOC >= volumeBelowPOC
            valueAreaVolume  += volumeAbovePOC
            VP.vahLevel += 1
        else
            valueAreaVolume  += volumeBelowPOC
            VP.valLevel -= 1

    vahPrice = lowestPrice + (VP.vahLevel + 1.) * priceStep
    pocPrice = lowestPrice + (VP.pocLevel + .5) * priceStep
    valPrice = lowestPrice + (VP.valLevel + .0) * priceStep

    profilePlottingLength = vp_profileLength > 360 ? 360 : vp_profileLength
    profileWidth = profilePlottingLength * vp_profileWidth
    profileHorizontalOffset = int(profileWidth + vp_profileHorizontalOffset)

    if vp_profileShow and profilePlacementRight and vp_pocShow == 'Developing'
        renderLine(last_bar_index, pocPrice, profileHorizontalOffset + int(last_bar_index - profileWidth + 1), pocPrice, xloc.bar_index, extend.none, vp_pocColor, line.style_solid, vp_pocWidth)

    if vp_vahShow
        renderLine(VP.startIndex, vahPrice, 
                   profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : last_bar_index, 
                   vahPrice, xloc.bar_index, extend.none, vp_vahColor, line.style_solid, 1)
    
    if vp_pocShow == 'Regular'
        renderLine(VP.startIndex, pocPrice, profilePlacementRight ? vp_profileShow ? profileHorizontalOffset + int(last_bar_index - profileWidth + 1) : last_bar_index : last_bar_index, pocPrice, xloc.bar_index, extend.none, vp_pocColor, line.style_solid, vp_pocWidth)

    if vp_valShow
        renderLine(VP.startIndex, valPrice, 
                   profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : last_bar_index, 
                   valPrice, xloc.bar_index, extend.none, vp_valColor, line.style_solid, 1)

    if vp_valueAreaBackground
        VP.boxes.push(box.new(VP.startIndex, valPrice, last_bar_index, vahPrice, vp_valueAreaBackgroundColor, 1, line.style_dotted, bgcolor = vp_valueAreaBackgroundColor))

    if vp_profileBackground
        VP.boxes.push(box.new(VP.startIndex, lowestPrice, last_bar_index, highestPrice, vp_profileBackgroundColor, 1, line.style_dotted, bgcolor = vp_profileBackgroundColor))

    if vp_profileLevels != 'None' and VP.pocLevel != -1 
        renderLabel(profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : vp_profileShow ? VP.startIndex : last_bar_index, 
                     highestPrice, str.tostring(highestPrice, format.mintick), color.new(chart.fg_color, 89), label.style_label_down, chart.fg_color, profileLevesSize, 'Profile High')

        renderLabel(profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : last_bar_index, 
                     vahPrice, str.tostring(vahPrice, format.mintick), color.new(vp_vahColor, 89), label.style_label_left, vp_vahColor, profileLevesSize, 'Value Area High')

        renderLabel(profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : last_bar_index, 
                     pocPrice, str.tostring(pocPrice, format.mintick), color.new(vp_pocColor, 89), label.style_label_left, vp_pocColor, profileLevesSize, 'Point of Control')
 
        renderLabel(profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : last_bar_index, 
                     valPrice, str.tostring(valPrice, format.mintick), color.new(vp_valColor, 89), label.style_label_left, vp_valColor, profileLevesSize, 'Value Area Low')

        renderLabel(profilePlacementRight ? (vp_profileShow ? profileHorizontalOffset : 0) + last_bar_index : vp_profileShow ? VP.startIndex : last_bar_index, 
                     lowestPrice, str.tostring(lowestPrice, format.mintick), color.new(chart.fg_color, 89), label.style_label_up, chart.fg_color, profileLevesSize, 'Profile Low')

    for volumeNodeLevel = 0 to vp_profileNumberOfRows - 1
    
        if vp_profileShow
            if vp_profileGradientColors == 'Gradient Colors'
                vp_valueAreaUpColor       := color.from_gradient(volumeDataArray.totalVolume.get(volumeNodeLevel) / volumeDataArray.totalVolume.max(), 0, 1, color.new(vp_valueAreaUpColor      , 95), color.new(vp_valueAreaUpColor      , 0))  
                vp_valueAreaDwonColor     := color.from_gradient(volumeDataArray.totalVolume.get(volumeNodeLevel) / volumeDataArray.totalVolume.max(), 0, 1, color.new(vp_valueAreaDwonColor    , 95), color.new(vp_valueAreaDwonColor    , 0))  
                vp_profileUpVolumeColor   := color.from_gradient(volumeDataArray.totalVolume.get(volumeNodeLevel) / volumeDataArray.totalVolume.max(), 0, 1, color.new(vp_profileUpVolumeColor  , 95), color.new(vp_profileUpVolumeColor  , 0))  
                vp_profileDownVolumeColor := color.from_gradient(volumeDataArray.totalVolume.get(volumeNodeLevel) / volumeDataArray.totalVolume.max(), 0, 1, color.new(vp_profileDownVolumeColor, 95), color.new(vp_profileDownVolumeColor, 0))  
 
            startProfileIndex = profilePlacementRight ? 
                                 profileHorizontalOffset + int(last_bar_index - volumeDataArray.bullishVolume.get(volumeNodeLevel) / volumeDataArray.totalVolume.max() * profileWidth) :
                                 VP.startIndex
            endProfileIndex   = profilePlacementRight ? 
                                 profileHorizontalOffset + last_bar_index : 
                                 int(startProfileIndex + volumeDataArray.bullishVolume.get(volumeNodeLevel) / volumeDataArray.totalVolume.max() * profileWidth)

            VP.boxes.push(box.new(startProfileIndex, lowestPrice + (volumeNodeLevel + .1) * priceStep, endProfileIndex, lowestPrice + (volumeNodeLevel + .9) * priceStep, 
                                   color(na), bgcolor = volumeNodeLevel >= VP.valLevel and volumeNodeLevel <= VP.vahLevel ? vp_valueAreaUpColor : vp_profileUpVolumeColor))

            startProfileIndex := profilePlacementRight ? startProfileIndex : endProfileIndex
            endProfileIndex   := profilePlacementRight ? 
                                 startProfileIndex - int( (volumeDataArray.totalVolume.get(volumeNodeLevel) - volumeDataArray.bullishVolume.get(volumeNodeLevel)) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                 startProfileIndex + int( (volumeDataArray.totalVolume.get(volumeNodeLevel) - volumeDataArray.bullishVolume.get(volumeNodeLevel)) / volumeDataArray.totalVolume.max() * profileWidth)

            VP.boxes.push(box.new(startProfileIndex, lowestPrice + (volumeNodeLevel + .1) * priceStep, endProfileIndex, lowestPrice + (volumeNodeLevel + .9) * priceStep, 
                                   color(na), bgcolor = volumeNodeLevel >= VP.valLevel and volumeNodeLevel <= VP.vahLevel ? vp_valueAreaDwonColor : vp_profileDownVolumeColor))
            volumeDataArray.endProfileIndex.set(volumeNodeLevel, endProfileIndex)

    if  vn_peaksShow != 'None' or  vn_troughsShow != 'None'
        var int startVolumeNodeIndex = na, var int endVolumeNodeIndex = na
        var bool peakUpperNth = na, var bool peakLowerNth = na

        peaksNumberOfNodes = int(vp_profileNumberOfRows * vn_peaksNumberOfNodes)

        tempPeakTotalVolume = volumeDataArray.totalVolume.copy()

        for index = 1 to peaksNumberOfNodes
            tempPeakTotalVolume.unshift(0.)
            tempPeakTotalVolume.push(0.)

        for volumeNodeLevel = 0 to vp_profileNumberOfRows - 1 + 2 * peaksNumberOfNodes 

            if vn_peaksShow != 'None' and volumeNodeLevel >= 2 * peaksNumberOfNodes 

                for currentVolumeNode = volumeNodeLevel - 2 * peaksNumberOfNodes to volumeNodeLevel - peaksNumberOfNodes - 1
                    if tempPeakTotalVolume.get(volumeNodeLevel - peaksNumberOfNodes) <= tempPeakTotalVolume.get(currentVolumeNode)
                        peakUpperNth := false
                        break
                    else
                        peakUpperNth := true

                for currentVolumeNode = volumeNodeLevel - peaksNumberOfNodes + 1 to volumeNodeLevel
                    if tempPeakTotalVolume.get(volumeNodeLevel - peaksNumberOfNodes) <= tempPeakTotalVolume.get(currentVolumeNode)
                        peakLowerNth := false
                        break
                    else
                        peakLowerNth := true

                if peakUpperNth and peakLowerNth and tempPeakTotalVolume.get(volumeNodeLevel - peaksNumberOfNodes) / tempPeakTotalVolume.max() > vn_VolumeNodeThreshold

                    startVolumeNodeIndex := vp_profileShow ? profilePlacementRight ? 
                                                             VP.startIndex : 
                                                             volumeDataArray.endProfileIndex.get(volumeNodeLevel - 2 * peaksNumberOfNodes) : //VP.startIndex + int(volumeDataArray.totalVolume.get(volumeNodeLevel - vn_peaksNumberOfNodes) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                             VP.startIndex

                    endVolumeNodeIndex   := vp_profileShow ? profilePlacementRight ? 
                                                             volumeDataArray.endProfileIndex.get(volumeNodeLevel - 2 * peaksNumberOfNodes) : //profileHorizontalOffset + int(last_bar_index - volumeDataArray.totalVolume.get(volumeNodeLevel - vn_peaksNumberOfNodes) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                             last_bar_index : 
                                             last_bar_index

                    vn_peakVolumeColor := vn_peaksShow == 'Peaks' ? vn_peakVolumeColor : color.from_gradient(tempPeakTotalVolume.get(volumeNodeLevel - peaksNumberOfNodes) / tempPeakTotalVolume.max(), 0, 1, color.new(vn_peakVolumeColor, 95), color.new(vn_peakVolumeColor, 65))  

                    VP.boxes.push(box.new(startVolumeNodeIndex, lowestPrice + (volumeNodeLevel - 2 * peaksNumberOfNodes + .1) * priceStep, endVolumeNodeIndex, lowestPrice + (volumeNodeLevel - 2 * peaksNumberOfNodes + .9) * priceStep, 
                                          color(na), bgcolor = vn_peakVolumeColor))

                    if vn_peaksShow == 'Clusters'

                        for currentVolumeNode = volumeNodeLevel - 2 * peaksNumberOfNodes to volumeNodeLevel

                            if currentVolumeNode >= peaksNumberOfNodes and currentVolumeNode <= vp_profileNumberOfRows - 1 + peaksNumberOfNodes
                                if not volumeDataArray.peakVolume.get(currentVolumeNode - peaksNumberOfNodes)

                                    startVolumeNodeIndex := vp_profileShow ? profilePlacementRight ? 
                                                                         VP.startIndex : 
                                                                         volumeDataArray.endProfileIndex.get(currentVolumeNode - peaksNumberOfNodes) : //VP.startIndex + int(volumeDataArray.totalVolume.get(currentVolumeNode) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                         VP.startIndex

                                    endVolumeNodeIndex   := vp_profileShow ? profilePlacementRight ? 
                                                                         volumeDataArray.endProfileIndex.get(currentVolumeNode - peaksNumberOfNodes) : //profileHorizontalOffset + int(last_bar_index - volumeDataArray.totalVolume.get(currentVolumeNode) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                                         last_bar_index : 
                                                         last_bar_index

                                    VP.boxes.push(box.new(startVolumeNodeIndex, lowestPrice + (currentVolumeNode - peaksNumberOfNodes + .0) * priceStep, endVolumeNodeIndex, lowestPrice + (currentVolumeNode - peaksNumberOfNodes + 1.) * priceStep, 
                                                       color(na), bgcolor = vn_peakVolumeColor))
                                    volumeDataArray.peakVolume.set(currentVolumeNode - peaksNumberOfNodes, true)

        tempPeakTotalVolume.clear()

        var bool troughUpperNth = na, var bool troughLowerNth = na
        troughsNumberOfNodes = int(vp_profileNumberOfRows * vn_troughsNumberOfNodes)

        tempTroughTotalVolume = volumeDataArray.totalVolume.copy()

        for index = 1 to troughsNumberOfNodes
            tempTroughTotalVolume.unshift(volumeDataArray.totalVolume.max())
            tempTroughTotalVolume.push(volumeDataArray.totalVolume.max())
            
        for volumeNodeLevel = 0 to vp_profileNumberOfRows - 1 + 2 * troughsNumberOfNodes 

            if vn_troughsShow != 'None' and volumeNodeLevel >= 2 * troughsNumberOfNodes 

                for currentVolumeNode = volumeNodeLevel - 2 * troughsNumberOfNodes to volumeNodeLevel - troughsNumberOfNodes - 1
                    if tempTroughTotalVolume.get(volumeNodeLevel - troughsNumberOfNodes) >= tempTroughTotalVolume.get(currentVolumeNode)
                        troughUpperNth := false
                        break
                    else
                        troughUpperNth := true

                for currentVolumeNode = volumeNodeLevel - troughsNumberOfNodes + 1 to volumeNodeLevel
                    if tempTroughTotalVolume.get(volumeNodeLevel - troughsNumberOfNodes) >= tempTroughTotalVolume.get(currentVolumeNode)
                        troughLowerNth := false
                        break
                    else
                        troughLowerNth := true

                if troughUpperNth and troughLowerNth and tempTroughTotalVolume.get(volumeNodeLevel - troughsNumberOfNodes) / tempTroughTotalVolume.max() > vn_VolumeNodeThreshold

                    startVolumeNodeIndex := vp_profileShow ? profilePlacementRight ? 
                                                             VP.startIndex : 
                                                             volumeDataArray.endProfileIndex.get(volumeNodeLevel - 2 * troughsNumberOfNodes) : //VP.startIndex + int(volumeDataArray.totalVolume.get(volumeNodeLevel - vn_troughsNumberOfNodes) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                             VP.startIndex

                    endVolumeNodeIndex   := vp_profileShow ? profilePlacementRight ? 
                                                             volumeDataArray.endProfileIndex.get(volumeNodeLevel - 2 * troughsNumberOfNodes) : //profileHorizontalOffset + int(last_bar_index - volumeDataArray.totalVolume.get(volumeNodeLevel - vn_troughsNumberOfNodes) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                             last_bar_index : 
                                             last_bar_index

                    vn_troughVolumeColor := vn_troughsShow == 'Troughs' ? vn_troughVolumeColor : color.from_gradient(tempTroughTotalVolume.get(volumeNodeLevel - troughsNumberOfNodes) / tempTroughTotalVolume.max(), 0, 1, color.new(vn_troughVolumeColor, 95), color.new(vn_troughVolumeColor, 31))  

                    VP.boxes.push(box.new(startVolumeNodeIndex, lowestPrice + (volumeNodeLevel - 2 * troughsNumberOfNodes + .1) * priceStep, endVolumeNodeIndex, lowestPrice + (volumeNodeLevel - 2 * troughsNumberOfNodes + .9) * priceStep, 
                                          color(na), bgcolor = vn_troughVolumeColor))


                    if vn_troughsShow == 'Clusters'

                        for currentVolumeNode = volumeNodeLevel - 2 * troughsNumberOfNodes to volumeNodeLevel

                            if currentVolumeNode >= troughsNumberOfNodes and currentVolumeNode <= vp_profileNumberOfRows - 1 + troughsNumberOfNodes

                                if not volumeDataArray.troughVolume.get(currentVolumeNode - troughsNumberOfNodes)
                                    startVolumeNodeIndex := vp_profileShow ? profilePlacementRight ? 
                                                                         VP.startIndex : 
                                                                         volumeDataArray.endProfileIndex.get(currentVolumeNode - troughsNumberOfNodes) : //VP.startIndex + int(volumeDataArray.totalVolume.get(currentVolumeNode) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                         VP.startIndex

                                    endVolumeNodeIndex   := vp_profileShow ? profilePlacementRight ? 
                                                                         volumeDataArray.endProfileIndex.get(currentVolumeNode - troughsNumberOfNodes) : //profileHorizontalOffset + int(last_bar_index - volumeDataArray.totalVolume.get(currentVolumeNode) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                                         last_bar_index : 
                                                         last_bar_index

                                    VP.boxes.push(box.new(startVolumeNodeIndex, lowestPrice + (currentVolumeNode - troughsNumberOfNodes + .0) * priceStep, endVolumeNodeIndex, lowestPrice + (currentVolumeNode - troughsNumberOfNodes + 1.) * priceStep, 
                                                      color(na), bgcolor = vn_troughVolumeColor))
                                    volumeDataArray.troughVolume.set(currentVolumeNode - troughsNumberOfNodes, true)

        tempTroughTotalVolume.clear()

    if vn_highestNVolumeNodes > 0
        for highestNode = 0 to vn_highestNVolumeNodes - 1
            startVolumeNodeIndex = vp_profileShow ? profilePlacementRight ? 
                                                     VP.startIndex : 
                                                     volumeDataArray.endProfileIndex.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max(highestNode))) : //VP.startIndex + int(volumeDataArray.totalVolume.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max(highestNode))) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                     VP.startIndex
            endVolumeNodeIndex   = vp_profileShow ? profilePlacementRight ? 
                                                     volumeDataArray.endProfileIndex.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max(highestNode))) : //profileHorizontalOffset + int(last_bar_index - volumeDataArray.totalVolume.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max(highestNode))) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                     last_bar_index : 
                                     last_bar_index

            VP.boxes.push(box.new(startVolumeNodeIndex, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max(highestNode)) + .1) * priceStep, endVolumeNodeIndex, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.max(highestNode)) + .9) * priceStep, color(na), bgcolor = vn_highestVolumeColor))

    if vn_lowestNVolumeNodes > 0

        lowestNVolumeNodeCount = 0
        lowestNVolumeNodeIndex = 0//volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min())
        lowestNVolumeNodeValue = 0.

        while lowestNVolumeNodeCount < vn_lowestNVolumeNodes

            if lowestNVolumeNodeIndex == vp_profileNumberOfRows
                break

            if volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex) != lowestNVolumeNodeValue
                lowestNVolumeNodeValue := volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex)

                startVolumeNodeIndex = vp_profileShow ? profilePlacementRight ? 
                                                     VP.startIndex : 
                                                     volumeDataArray.endProfileIndex.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex))) : //VP.startIndex + int(volumeDataArray.totalVolume.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex))) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                     VP.startIndex

                endVolumeNodeIndex   = vp_profileShow ? profilePlacementRight ? 
                                                     volumeDataArray.endProfileIndex.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex))) : //profileHorizontalOffset + int(last_bar_index - volumeDataArray.totalVolume.get(volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex))) / volumeDataArray.totalVolume.max() * profileWidth) : 
                                                     last_bar_index : 
                                     last_bar_index

                VP.boxes.push(box.new(startVolumeNodeIndex, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex)) + .1) * priceStep, endVolumeNodeIndex, lowestPrice + (volumeDataArray.totalVolume.indexof(volumeDataArray.totalVolume.min(lowestNVolumeNodeIndex)) + .9) * priceStep, color(na), bgcolor = vn_lowestVolumeColor))
                lowestNVolumeNodeCount += 1
            lowestNVolumeNodeIndex += 1

    log.info("yaz_kizim {0} {1}", VP.boxes.size(), volumeDataArray.totalVolume.size() )

//---------------------------------------------------------------------------------------------------------------------}