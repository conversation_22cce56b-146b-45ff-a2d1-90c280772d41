//@version=5

//------ START MACD-X ------//
// indicator("MACD-X Overlay, More Than MACD by DGT", "MACD-X ʙʏ DGT ☼☾", true, max_lines_count = 500, max_boxes_count = 250, max_bars_back = 500)

macdx_macdType      = input.string("MACD-macdx_SOURCE", "MACD Calculation Method", options=["MACD-TRADITIONAL", "MACD-AS (macdx_HISTOGRAM)", "MACD-LEADER", "MACD-macdx_SOURCE"])
macdx_fast_length   = input.int(12, "Fast Length", minval = 1)
macdx_slow_length   = input.int(26, "Slow Length", minval = 1)
macdx_source        = input(close, "macdx_Source")
macdx_signal_length = input.int(9, "Signal Smoothing",  minval = 1, maxval = 50)
macdx_sma_source    = input.string("EMA", "Oscillator MA Type" , options=["SMA", "EMA"])
macdx_sma_signal    = input.string("EMA", "Signal Line MA Type", options=["SMA", "EMA"])
macdx_macdSigCross  = input.bool(false, "Display MACD/Signal Corsses")
macdx_highlight     = input.bool(false, "macdx_Highlight MACD/Signal Area")
macdx_lookbackLength= input.int(200, 'Overlay Indicator Display Length', minval = 10, maxval = 250) 
macdx_oscPlacement  = input.string('Bottom', 'Placement', options = ['Top', 'Bottom'], inline='VOL')
macdx_oscHight      = 12 - input.int(7, 'Hight' , minval = 1, maxval = 10  , inline='VOL' )
macdx_verticalAdj   = input.int(3, "Vertical Indicator Position", minval = 0, maxval = 10) / 10

ma(s, l, m) => m == "EMA" ? ta.ema(s, l) : ta.sma(s, l)

macdx_fast_ma = ma(macdx_source, macdx_fast_length, macdx_sma_source)
macdx_slow_ma = ma(macdx_source, macdx_slow_length, macdx_sma_source)
macdx_macd    = macdx_fast_ma - macdx_slow_ma

macdx_macd := if macdx_macdType == "MACD-TRADITIONAL"
    macdx_macd
else if macdx_macdType == "MACD-AS (HISTOGRAM)"
    macdx_macd - ma(macdx_macd, macdx_signal_length, macdx_sma_source)
else if macdx_macdType == "MACD-LEADER"
    macdx_macd + ma(macdx_source - macdx_fast_ma, macdx_fast_length, macdx_sma_source) - ma(macdx_source - macdx_slow_ma, macdx_slow_length, macdx_sma_source)
else
    ma(macdx_source - math.avg(macdx_fast_ma, macdx_slow_ma), macdx_signal_length, macdx_sma_source)

macdx_signal = ma(macdx_macd, macdx_signal_length, macdx_sma_signal)
macdx_hist   = macdx_macd - macdx_signal

macdx_longAlertCondition  = ta.crossover(macdx_macd, macdx_signal)
alertcondition(macdx_longAlertCondition   , "Long : Early Warning"        , "MACD-X - Not Confirmed Probable Long Trade Opportunity\n{{exchange}}:{{ticker}}->\nPrice = {{close}},\nTime = {{time}}")
alertcondition(macdx_longAlertCondition[1], "Long : Trading Opportunity"  , "MACD-X - Probable Long Trade Opportunity\n{{exchange}}:{{ticker}}->\nPrice = {{close}},\nTime = {{time}}")
plotshape(macdx_macdSigCross ? macdx_longAlertCondition : na, "Long" , shape.labelup  , location.belowbar, color.new(color.green, 0), size=size.small , show_last=macdx_lookbackLength)

macdx_shortAlertCondition = ta.crossunder(macdx_macd, macdx_signal)
alertcondition(macdx_shortAlertCondition   , "Short : Early Warning"      , "MACD-X - Not Confirmed Probable Short Trade Opportunity\n{{exchange}}:{{ticker}}->\nPrice = {{close}},\nTime = {{time}}")
alertcondition(macdx_shortAlertCondition[1], "Short : Trading Opportunity", "MACD-X - Probable Short Trade Opportunity\n{{exchange}}:{{ticker}}->\nPrice = {{close}},\nTime = {{time}}")
plotshape(macdx_macdSigCross ? macdx_shortAlertCondition : na, "Short", shape.labeldown, location.abovebar, color.new(color.red  , 0), size=size.small , show_last=macdx_lookbackLength)

var macdx_a_lines     = array.new_line()
var macdx_a_hist      = array.new_box()
var macdx_a_fill      = array.new_linefill()

macdx_priceHighest    = ta.highest(high, macdx_lookbackLength)
macdx_priceLowest     = ta.lowest (low , macdx_lookbackLength)
macdx_priceChangeRate = (macdx_priceHighest - macdx_priceLowest) / macdx_priceHighest
macdx_priceLowest    := macdx_priceLowest  * (1 - macdx_priceChangeRate * macdx_verticalAdj)
macdx_priceHighest   := macdx_priceHighest * (1 + macdx_priceChangeRate * macdx_verticalAdj)
macdx_oscHighest      = ta.highest(macdx_macd, macdx_lookbackLength)
macdx_histColor       = macdx_hist >= 0 ? macdx_hist[1] < macdx_hist ? #006400 : color.green : macdx_hist[1] < macdx_hist ? color.red : #910000

if barstate.islast
    if array.size(macdx_a_lines) > 0
        for i = 1 to array.size(macdx_a_lines)
            line.delete(array.shift(macdx_a_lines))

    if array.size(macdx_a_hist) > 0
        for i = 1 to array.size(macdx_a_hist)
            box.delete(array.shift(macdx_a_hist))

    if array.size(macdx_a_fill) > 0
        for i = 1 to array.size(macdx_a_fill)
            linefill.delete(array.shift(macdx_a_fill))

    macdx_hightAdj = macdx_priceChangeRate / macdx_oscHight

    for macdx_barIndex = 0 to macdx_lookbackLength - 1
        if array.size(macdx_a_lines) < 501
            array.push(macdx_a_hist , box.new (bar_index[macdx_barIndex],      macdx_oscPlacement == 'Top' ? macdx_priceHighest : macdx_priceLowest, 
                                         bar_index[macdx_barIndex],     (macdx_oscPlacement == 'Top' ? macdx_priceHighest : macdx_priceLowest) * (1 + macdx_hist[macdx_barIndex]       / macdx_oscHighest * macdx_hightAdj), macdx_histColor[macdx_barIndex], 2))
            array.push(macdx_a_lines, line.new(bar_index[macdx_barIndex],     (macdx_oscPlacement == 'Top' ? macdx_priceHighest : macdx_priceLowest) * (1 + macdx_macd[macdx_barIndex]       / macdx_oscHighest * macdx_hightAdj), 
                                         bar_index[macdx_barIndex + 1], (macdx_oscPlacement == 'Top' ? macdx_priceHighest : macdx_priceLowest) * (1 + macdx_macd[macdx_barIndex + 1]   / macdx_oscHighest * macdx_hightAdj), xloc.bar_index, extend.none, #2962FF, line.style_solid, 1))
            array.push(macdx_a_lines, line.new(bar_index[macdx_barIndex],     (macdx_oscPlacement == 'Top' ? macdx_priceHighest : macdx_priceLowest) * (1 + macdx_signal[macdx_barIndex]     / macdx_oscHighest * macdx_hightAdj), 
                                         bar_index[macdx_barIndex + 1], (macdx_oscPlacement == 'Top' ? macdx_priceHighest : macdx_priceLowest) * (1 + macdx_signal[macdx_barIndex + 1] / macdx_oscHighest * macdx_hightAdj), xloc.bar_index, extend.none, #FF6D00, line.style_solid, 1))
            if macdx_highlight
                array.push(macdx_a_fill, linefill.new(array.get(macdx_a_lines, 2 * macdx_barIndex), array.get(macdx_a_lines, 2 * macdx_barIndex + 1), macdx_macd[macdx_barIndex] > macdx_signal[macdx_barIndex] ? color.new(#2962FF, 50) : color.new(#FF6D00, 50)))

var table macdx_logo = table.new(position.bottom_right, 1, 1)
if barstate.islast
    table.cell(macdx_logo, 0, 0, '☼☾  ', text_size=size.normal, text_color=color.teal)

//------ END MACD-X ------//