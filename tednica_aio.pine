//@version=5
indicator('TEDnica AIO', overlay=true)

// mix_lrc = input(true, "Show Linear Regression Channel?", group = "MIX SETTINGS")


//------ START Linear Regression Channel ------//

// indicator('Linear Regression Channel', overlay=true, max_bars_back = 1000, max_lines_count = 300)
lrc_src = input(defval=close, title='Source', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_len = input.int(defval=400, title='Length', minval=10, group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_devlen = input.float(defval=2., title='Deviation', minval=0.1, step=0.1, group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_extendit = input(defval=true, title='Extend Lines', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_showfibo = input(defval=false, title='Show Fibonacci Levels', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_showbroken = input.bool(defval=false, title='Show Broken Channel', inline='brk', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_brokencol = input.color(defval=#0fbcf9, title='', inline='brk', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_upcol = input.color(defval=#05c46b, title='Up/Down Trend Colors', inline='trcols', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_dncol = input.color(defval=#ff3f34, title='', inline='trcols', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")
lrc_width = input(defval=2, title='Line Width', group="LINEAR REGRESSION CHANNEL SETTINGS (LRC)")

var lrc_fibo_ratios = array.new_float(0)
var lrc_colors = array.new_color(2)
if barstate.isfirst
    array.unshift(lrc_colors, lrc_upcol)
    array.unshift(lrc_colors, lrc_dncol)
    array.push(lrc_fibo_ratios, 0.236)
    array.push(lrc_fibo_ratios, 0.382)
    array.push(lrc_fibo_ratios, 0.618)
    array.push(lrc_fibo_ratios, 0.786)

get_channel(lrc_src, lrc_len) =>
    lrc_mid = math.sum(lrc_src, lrc_len) / lrc_len
    lrc_slope = ta.linreg(lrc_src, lrc_len, 0) - ta.linreg(lrc_src, lrc_len, 1)
    lrc_intercept = lrc_mid - lrc_slope * math.floor(lrc_len / 2) + (1 - lrc_len % 2) / 2 * lrc_slope
    lrc_endy = lrc_intercept + lrc_slope * (lrc_len - 1)
    lrc_dev = 0.0
    for x = 0 to lrc_len - 1 by 1
        lrc_dev += math.pow(lrc_src[x] - (lrc_slope * (lrc_len - x) + lrc_intercept), 2)
        lrc_dev
    lrc_dev := math.sqrt(lrc_dev / lrc_len)
    [lrc_intercept, lrc_endy, lrc_dev, lrc_slope]

[y1_, y2_, lrc_dev, lrc_slope] = get_channel(lrc_src, lrc_len)

lrc_outofchannel = lrc_slope > 0 and close < y2_ - lrc_dev * lrc_devlen ? 0 : lrc_slope < 0 and close > y2_ + lrc_dev * lrc_devlen ? 2 : -1

var lrc_reglines = array.new_line(3)
var lrc_fibolines = array.new_line(4)

for x = 0 to 2 by 1
    if not lrc_showbroken or lrc_outofchannel != x or nz(lrc_outofchannel[1], -1) != -1
        line.delete(array.get(lrc_reglines, x))
    else
        line.set_color(array.get(lrc_reglines, x), color=lrc_brokencol)
        line.set_width(array.get(lrc_reglines, x), width=2)
        line.set_style(array.get(lrc_reglines, x), style=line.style_dotted)
        line.set_extend(array.get(lrc_reglines, x), extend=extend.none)

    array.set(lrc_reglines, x, line.new(x1=bar_index - (lrc_len - 1), y1=y1_ + lrc_dev * lrc_devlen * (x - 1), x2=bar_index, y2=y2_ + lrc_dev * lrc_devlen * (x - 1), color=array.get(lrc_colors, math.round(math.max(math.sign(lrc_slope), 0))), style=x % 2 == 1 ? line.style_solid : line.style_dashed, width=lrc_width, extend=lrc_extendit ? extend.right : extend.none))

if lrc_showfibo
    for x = 0 to 3 by 1
        line.delete(array.get(lrc_fibolines, x))
        array.set(lrc_fibolines, x, line.new(x1=bar_index - (lrc_len - 1), y1=y1_ - lrc_dev * lrc_devlen + lrc_dev * lrc_devlen * 2 * array.get(lrc_fibo_ratios, x), x2=bar_index, y2=y2_ - lrc_dev * lrc_devlen + lrc_dev * lrc_devlen * 2 * array.get(lrc_fibo_ratios, x), color=array.get(lrc_colors, math.round(math.max(math.sign(lrc_slope), 0))), style=line.style_dotted, width=lrc_width, extend=lrc_extendit ? extend.right : extend.none))

var label lrc_sidelab = label.new(x=bar_index - (lrc_len - 1), y=y1_, text='S', size=size.large)
lrc_txt = lrc_slope > 0 ? lrc_slope > lrc_slope[1] ? '⇑' : '⇗' : lrc_slope < 0 ? lrc_slope < lrc_slope[1] ? '⇓' : '⇘' : '⇒'
lrc_stl = lrc_slope > 0 ? lrc_slope > lrc_slope[1] ? label.style_label_up : label.style_label_upper_right : lrc_slope < 0 ? lrc_slope < lrc_slope[1] ? label.style_label_down : label.style_label_lower_right : label.style_label_right
label.set_style(lrc_sidelab, lrc_stl)
label.set_text(lrc_sidelab, lrc_txt)
label.set_x(lrc_sidelab, bar_index - (lrc_len - 1))
label.set_y(lrc_sidelab, lrc_slope > 0 ? y1_ - lrc_dev * lrc_devlen : lrc_slope < 0 ? y1_ + lrc_dev * lrc_devlen : y1_)
label.set_color(lrc_sidelab, lrc_slope > 0 ? lrc_upcol : lrc_slope < 0 ? lrc_dncol : color.blue)


// alertcondition(lrc_outofchannel, title='Channel Broken', message='Channel Broken')
// // direction
// lrc_trendisup = math.sign(lrc_slope) != math.sign(lrc_slope[1]) and lrc_slope > 0
// lrc_trendisdown = math.sign(lrc_slope) != math.sign(lrc_slope[1]) and lrc_slope < 0
// alertcondition(lrc_trendisup, title='Up trend', message='Up trend')
// alertcondition(lrc_trendisdown, title='Down trend', message='Down trend')

//------ END Linear Regression Channel ------//








//------ START Q-Trend ------//

//indicator("Q-Trend", overlay = 1) 

// Inputs
qt_src = input(close, "Source", group = "Q-Trend Settings")
qt_p = input.int(200, "Trend period", group = "Q-Trend Settings", tooltip = "Changes STRONG signals' sensitivity.", minval = 1)
qt_atr_p = input.int(14, "ATR Period", group = "Q-Trend Settings", minval = 1)
qt_mult = input.float(1.0, "ATR Multiplier", step = 0.1, group = "Q-Trend Settings", tooltip = "Changes sensitivity: higher period = higher sensitivty.")
qt_mode = input.string("Type A", "Signal qt_mode", options = ["Type A", "Type B"], group = "Q-Trend Settings")
qt_use_ema_smoother = input.string("No", "Smooth source with EMA?", options = ["Yes", "No"], group = "Q-Trend Settings")
qt_src_ema_period = input(3, "EMA Smoother period", group = "Q-Trend Settings")
qt_color_bars = input(false, "Color bars?", group = "Q-Trend Settings")
qt_show_tl = input(false, "Show trend line?", group = "Q-Trend Settings")
qt_signals_view = input.string("All", "Signals to show", options = ["All", "Up/Down", "Long/Short", "None"], group = "Q-Trend Settings")
qt_signals_shape = input.string("Labels", "Signal's shape", options = ["Labels", "Arrows"], group = "Q-Trend Settings")
qt_buy_col = input(#218c74, "Long colour", group = "Q-Trend Settings", inline = "BS")
qt_sell_col = input(#b33939, "Short colour", group = "Q-Trend Settings", inline = "BS")

// Calculations
qt_src := qt_use_ema_smoother == "Yes" ? ta.ema(qt_src, qt_src_ema_period) : qt_src // Source;

qt_h = ta.highest(qt_src, qt_p) // Highest of qt_src p-bars back;
qt_l = ta.lowest(qt_src, qt_p) // Lowest of qt_src p-bars back.
qt_d = qt_h - qt_l

qt_ls = "" // Tracker of last signal

qt_m = (qt_h + qt_l) / 2 // Initial trend line;
qt_m := bar_index > qt_p ? qt_m[1] : qt_m

qt_atr = ta.atr(qt_atr_p)[1] // ATR;
qt_epsilon = qt_mult * qt_atr // Epsilon is a mathematical variable used in many different theorems in order to simplify work with mathematical object. Here it used as sensitivity measure.

qt_change_up = (qt_mode == "Type B" ? ta.cross(qt_src, qt_m + qt_epsilon) : ta.crossover(qt_src, qt_m + qt_epsilon)) or qt_src > qt_m + qt_epsilon // If price breaks trend line + qt_epsilon (so called higher band), then it is time to update the value of a trend line;
qt_change_down = (qt_mode == "Type B" ? ta.cross(qt_src, qt_m - qt_epsilon) : ta.crossunder(qt_src, qt_m - qt_epsilon)) or qt_src < qt_m - qt_epsilon // If price breaks trend line - qt_epsilon (so called higher band), then it is time to update the value of a trend line.
sb = open < qt_l + qt_d / 8 and open >= qt_l
ss = open > qt_h - qt_d / 8 and open <= qt_h
qt_strong_buy = sb or sb[1] or sb[2] or sb[3] or sb[4]
qt_strong_sell = ss or ss[1] or ss[2] or ss[3] or ss[4]

qt_m := (qt_change_up or qt_change_down) and qt_m != qt_m[1] ? qt_m : qt_change_up ? qt_m + qt_epsilon : qt_change_down ? qt_m - qt_epsilon : nz(qt_m[1], qt_m) // Updating the trend line.

qt_ls := qt_change_up ? "B" : qt_change_down ? "S" : qt_ls[1] // Last signal. Helps avoid multiple labels in a row with the same signal;
qt_colour = qt_ls == "B" ? qt_buy_col : qt_sell_col // Colour of the trend line.
qt_buy_shape   = qt_signals_shape == "Labels" ? shape.labelup     : shape.triangleup
qt_sell_shape  = qt_signals_shape == "Labels" ? shape.labeldown   : shape.triangledown

// Plottings
plot(qt_show_tl ? qt_m : na, "(QT) trend line", qt_colour, 3) // Plotting the trend line.

// Signals with label shape
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_up and qt_ls[1] != "B" and not qt_strong_buy, "(QT) Long signal"       , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny, text = "↑", textcolor = color.white)      // Plotting the LONG signal;
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_down and qt_ls[1] != "S" and not qt_strong_sell, "(QT) Short signal"   , color = qt_colour, style = qt_sell_shape, size = size.tiny, text = "↓", textcolor = color.white)                                   // Plotting the SHORT signal.
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_up and qt_ls[1] != "B" and qt_strong_buy, "(QT) Strong Long signal"      , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny, text = "LONG", textcolor = color.white)   // Plotting the STRONG LONG signal;
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_down and qt_ls[1] != "S" and qt_strong_sell, "(QT) Strong Short signal"  , color = qt_colour, style = qt_sell_shape, size = size.tiny, text = "SHORT", textcolor = color.white)                                 // Plotting the STRONG SHORT signal.

// Signal with arrow shape
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_up and qt_ls[1] != "B" and not qt_strong_buy, "(QT) Long signal"       , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny) // Plotting the LONG signal;
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_down and qt_ls[1] != "S" and not qt_strong_sell, "(QT) Short signal"   , color = qt_colour, style = qt_sell_shape, size = size.tiny)                               // Plotting the SHORT signal.
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_up and qt_ls[1] != "B" and qt_strong_buy, "(QT) Strong Long signal"      , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny) // Plotting the STRONG LONG signal;
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_down and qt_ls[1] != "S" and qt_strong_sell, "(QT) Strong Short signal"  , color = qt_colour, style = qt_sell_shape, size = size.tiny)                               // Plotting the STRONG SHORT signal.

barcolor(qt_color_bars ? qt_colour : na, title="(QT) Bar Colors") // Bar coloring

// Alerts
// alertcondition(qt_change_up and qt_ls[1] != "B", "Q-Trend LONG", "Q-Trend LONG signal were given.") // Long alert.
// alertcondition(qt_change_down and qt_ls[1] != "S", "Q-Trend SHORT", "Q-Trend SHORT signal were given.") // Short alert.
// alertcondition((qt_change_up and qt_ls[1] != "B") or (qt_change_down and qt_ls[1] != "S"), "Q-Trend Signal", "Q-Trend gave you a signal!")
// alertcondition(qt_change_up and qt_ls[1] != "B" and qt_strong_buy, "Strong LONG signal", "Q-Trend gave a Strong Long signal!")
// alertcondition(qt_change_down and qt_ls[1] != "S" and qt_strong_sell, "Strong SHORT signal", "Q-Trend gave a Strong Short signal!")

//------ END Q-Trend ------//


//------ START Hull Suite ------//

//indicator('Hull Suite by InSilico', overlay=true)

//INPUT
hs_src = input(close, title='Source', group="Hull Suite Settings")
hs_modeSwitch = input.string('Hma', title='Hull Variation', options=['Hma', 'Thma', 'Ehma'], group="Hull Suite Settings")
hs_length = input(55, title='Length(180-200 for floating S/R , 55 for swing entry)', group="Hull Suite Settings")
hs_lengthMult = input(1.2, title='Length multiplier (Used to view higher timeframes with straight band)', group="Hull Suite Settings")

hs_useHtf = input(false, title='Show Hull MA from X timeframe? (good for scalping)', group="Hull Suite Settings")
hs_htf = input.timeframe('240', title='Higher timeframe', group="Hull Suite Settings")

hs_switchColor = input(true, 'Color Hull according to trend?', group="Hull Suite Settings")
hs_candleCol = input(false, title='Color candles based on Hull\'s Trend?', group="Hull Suite Settings")
hs_visualSwitch = input(true, title='Show as a Band?', group="Hull Suite Settings")
hs_thicknesSwitch = input(2, title='Line Thickness', group="Hull Suite Settings")
hs_transpSwitch = input.int(30, title='Band Transparency', step=5, group="Hull Suite Settings")

//FUNCTIONS
//HMA
HMA(_hs_src, _hs_length) =>
    ta.wma(2 * ta.wma(_hs_src, _hs_length / 2) - ta.wma(_hs_src, _hs_length), math.round(math.sqrt(_hs_length)))
//EHMA    
EHMA(_hs_src, _hs_length) =>
    ta.ema(2 * ta.ema(_hs_src, _hs_length / 2) - ta.ema(_hs_src, _hs_length), math.round(math.sqrt(_hs_length)))
//THMA    
THMA(_hs_src, _hs_length) =>
    ta.wma(ta.wma(_hs_src, _hs_length / 3) * 3 - ta.wma(_hs_src, _hs_length / 2) - ta.wma(_hs_src, _hs_length), _hs_length)

//SWITCH
Mode(hs_modeSwitch, hs_src, len) =>
    hs_modeSwitch == 'Hma' ? HMA(hs_src, len) : hs_modeSwitch == 'Ehma' ? EHMA(hs_src, len) : hs_modeSwitch == 'Thma' ? THMA(hs_src, len / 2) : na

//OUT
_hs_hull = Mode(hs_modeSwitch, hs_src, int(hs_length * hs_lengthMult))
hs_HULL = hs_useHtf ? request.security(syminfo.ticker, hs_htf, _hs_hull) : _hs_hull
hs_MHULL = hs_HULL[0]
hs_SHULL = hs_HULL[2]

//COLOR
hs_hullColor = hs_switchColor ? hs_HULL > hs_HULL[2] ? #218c74 : #b33939 : #cd6133

//PLOT
// < Frame
hs_Fi1 = plot(hs_MHULL, title='(HS) MHULL', color=hs_hullColor, linewidth=hs_thicknesSwitch)
hs_Fi2 = plot(hs_visualSwitch ? hs_SHULL : na, title='(HS) SHULL', color=hs_hullColor, linewidth=hs_thicknesSwitch)

// alertcondition(ta.crossover(hs_MHULL, hs_SHULL), title='Hull trending up.', message='Hull trending up.')
// alertcondition(ta.crossover(hs_SHULL, hs_MHULL), title='Hull trending down.', message='Hull trending down.')

///< Ending Filler
fill(hs_Fi1, hs_Fi2, title='Band Filler', color=color.new(hs_hullColor, hs_transpSwitch))
///BARCOLOR
barcolor(color=hs_candleCol ? hs_switchColor ? hs_hullColor : na : na, title="(HS) Bar Color")

//------ END Hull Suite ------//

//------ START Protein+ ------//

//indicator("[UST] Protein+", max_lines_count=500, max_labels_count=500, overlay=true, max_bars_back=1000)

string pp_groupName = "PROTEIN+ SETTINGS"
pp_show_current_sr = input.bool(true, title = "Show Current TF S/R", group = pp_groupName)
pp_swing_length = input.int(10, title = 'Swing Length', group = pp_groupName, minval = 1, maxval = 50)
pp_history_of_demand_to_keep = input.int(20, title = 'Lookback', minval = 5, maxval = 50, group = pp_groupName)
pp_box_width = input.float(7, title = 'S/R Range', group = pp_groupName, minval = 1, maxval = 10, step = 0.5)

pp_enableHtf1 = input.bool(false, group = pp_groupName, inline = "pp_htf1", title = "")
pp_htf1 = input.timeframe("W", "TF1", group = pp_groupName, inline = "pp_htf1")
pp_enableHtf2 = input.bool(false, group = pp_groupName, inline = "pp_htf2", title = "")
pp_htf2 = input.timeframe("D", "TF2", group = pp_groupName, inline = "pp_htf2")
pp_enableHtf3 = input.bool(false, group = pp_groupName, inline = "pp_htf3", title = "")
pp_htf3 = input.timeframe("240", "TF3", group = pp_groupName, inline = "pp_htf3")
pp_enableHtf4 = input.bool(false, group = pp_groupName, inline = "pp_htf4", title = "")
pp_htf4 = input.timeframe("180", "TF4", group = pp_groupName, inline = "pp_htf4")
pp_enableHtf5 = input.bool(false, group = pp_groupName, inline = "htf5", title = "")
htf5 = input.timeframe("120", "TF5", group = pp_groupName, inline = "htf5")
pp_enableHtf6 = input.bool(false, group = pp_groupName, inline = "pp_htf6", title = "")
pp_htf6 = input.timeframe("60", "TF6", group = pp_groupName, inline = "pp_htf6")
pp_enableHtf7 = input.bool(true, group = pp_groupName, inline = "pp_htf7", title = "")
pp_htf7 = input.timeframe("60", "TF7", group = pp_groupName, inline = "pp_htf7")
pp_enableHtf8 = input.bool(true, group = pp_groupName, inline = "pp_htf8", title = "")
pp_htf8 = input.timeframe("15", "TF8", group = pp_groupName, inline = "pp_htf8")
pp_enableHtf9 = input.bool(true, group = pp_groupName, inline = "pp_htf9", title = "")
pp_htf9 = input.timeframe("5", "TF9", group = pp_groupName, inline = "pp_htf9")

pp_supply_color = input.color(#cc8e35, title = 'Support Zone', group = pp_groupName, inline = '3')
pp_supply_outline_color = input.color(#cc8e35, title = 'BoS', group = pp_groupName, inline = '3')

pp_demand_color = input.color(#227093, title = 'Resistance Zone', group = pp_groupName, inline = '4')
pp_demand_outline_color = input.color(#227093, title = 'BoR', group = pp_groupName, inline = '4')

// bos_label_color = input.color(#f7f1e3, title = 'Fracture Zone', group = pp_groupName, inline = '5')
pp_poi_label_color = input.color(#f7f1e3, title = 'S/R Zone Label', group = pp_groupName, inline = '7')

type zone_with_strength
    box zone
    int count = 0
    string tf

// FUNCTION TO ADD NEW AND REMOVE LAST IN ARRAY
array_add_pop(array, new_value_to_add) =>
    array.unshift(array, new_value_to_add)
    array.pop(array)

// FUNCTION MAKE SURE SUPPLY ISNT OVERLAPPING
check_overlapping(new_poi, box_array, atr) =>
    atr_threshold = atr * 2
    okay_to_draw = true
    
    for i = 0 to array.size(box_array) - 1
        zone_with_strength zws = array.get(box_array, i)
        if not na(zws)
            box b = zws.zone
            top = box.get_top(b)
            bottom = box.get_bottom(b)
            poi = (top + bottom) / 2

            upper_boundary = poi + atr_threshold
            lower_boundary = poi - atr_threshold

            if new_poi >= lower_boundary and new_poi <= upper_boundary
                okay_to_draw := false
                break
            else 
                okay_to_draw := true

    okay_to_draw

// FUNCTION TO DRAW SUPPLY OR DEMAND ZONE
support_resistance(value_array, bn_array, box_array, label_array, count_array, box_type, atr, tf_label, tfbarindex, is_htf) =>
    
    atr_buffer = atr * (pp_box_width / 10)
    
    box_left = is_htf ? bar_index : array.get(bn_array, 0)
    box_right = bar_index

    var float box_top = 0.00
    var float box_bottom = 0.00
    var float poi = 0.00

    if box_type == 1
        box_top := array.get(value_array, 0)
        box_bottom := box_top - atr_buffer
        poi := (box_top + box_bottom) / 2
    else if box_type == -1
        box_bottom := array.get(value_array, 0)
        box_top := box_bottom + atr_buffer
        poi := (box_top + box_bottom) / 2

    okay_to_draw = check_overlapping(poi, box_array, atr)
    // okay_to_draw = true
    //delete oldest box, and then create a new box and add it to the array
    if box_type == 1 and okay_to_draw
        zone_with_strength last = array.get(box_array, array.size(box_array) - 1)
        if not na(last)
            box.delete(last.zone)
        box b = box.new(left = box_left, top = box_top, right = bar_index, bottom = box_bottom, border_color = pp_supply_outline_color,
                     bgcolor = color.new(pp_supply_color, 80), extend = extend.none, text = tf_label, text_halign = text.align_right, text_valign = text.align_center, text_color = pp_poi_label_color, text_size = size.small, xloc = xloc.bar_index)
        zone_with_strength zws = zone_with_strength.new(b, 0, tf_label)
        array_add_pop(box_array, zws)

    else if box_type == -1 and okay_to_draw
        zone_with_strength last = array.get(box_array, array.size(box_array) - 1)
        if not na(last)
            box.delete(last.zone)
        box b = box.new(left = box_left, top = box_top, right = bar_index, bottom = box_bottom, border_color = pp_demand_outline_color,
                     bgcolor = color.new(pp_demand_color, 80), extend = extend.none,  text = tf_label, text_halign = text.align_right, text_valign = text.align_center, text_color = pp_poi_label_color, text_size = size.small, xloc = xloc.bar_index)
        zone_with_strength zws = zone_with_strength.new(b, 0, tf_label)
        array_add_pop(box_array, zws)

//      FUNCTION TO CHANGE SUPPLY/DEMAND TO A BOS IF BROKEN
sd_to_bos(box_array, bos_array, count_array, label_array, zone_type, tfclose, tfbarindex) =>
    for i = 0 to array.size(box_array) - 1
        zone_with_strength zws = array.get(box_array, i)
        if not na(zws)
            box b = zws.zone
            level_to_break_top = box.get_top(b)
            level_to_break_bottom = box.get_bottom(b)
            // if ta.crossover(close, level_to_break)
            if (zone_type == 1 and close >= level_to_break_top) or (zone_type == -1 and close <= level_to_break_bottom)
                copied_zone = zone_with_strength.copy(zws)
                box cz = copied_zone.zone
                mid = (box.get_top(b) + box.get_bottom(b)) / 2
                // line bosline = line.new(box.get_left(cz), mid, bar_index, mid, xloc.bar_index, extend.none, zone_type == 1 ? pp_supply_outline_color : pp_demand_outline_color, line.style_dotted)
                // array_add_pop(bos_array, bosline)
                // box bos = array.get(bos_array, 0)
                // box.set_top(bos, mid)
                // box.set_bottom(bos, mid)
                // box.set_right(bos, bar_index)
                // box.set_extend(bos, extend.none)
                // box.set_text(bos, '')
                // box.set_text_color( bos, bos_label_color)
                // box.set_text_size(bos, size.small)
                // box.set_text_halign(bos, text.align_center)
                // box.set_text_valign(bos, text.align_center)
                box.delete(b)
                array.set(box_array, i, na)
                // array.remove(box_array, i)
                // box.delete(array.get(label_array, i))

//      FUNCTION MANAGE CURRENT BOXES BY CHANGING ENDPOINT
extend_box_endpoint(box_array, count_array, tfbarindex, tfhigh, tflow) =>
    for i = 0 to array.size(box_array) - 1
        zone_with_strength zws = array.get(box_array, i)
        if not na(zws)
            box b = zws.zone
            top = box.get_top(b)
            bottom = box.get_bottom(b)
            count = zws.count
            if (high[pp_swing_length - 1] >= bottom and high[pp_swing_length - 1] <= top) or (low[pp_swing_length - 1] >= bottom and low[pp_swing_length - 1] <= top)
                count := count + 1
                zws.count := count
            // count := count + 1
            // zws.count := count
            box.set_right(b, bar_index + 20)
            box.set_text(b, str.format("{0} / {1}", count, zws.tf))
            zws.zone := b
            array.set(box_array, i, zws)

//
//END FUNCTIONS
//  

//
// CALCULATIONS FOR SUPPORT AND RESISTANCE
//
calculateSR(pp_swing_high_values, pp_swing_low_values, pp_swing_high_bns, swing_low_bns, supply_zone, demand_zone, current_supply_touch_count, current_demand_touch_count, pp_current_supply_poi, pp_current_demand_poi, supply_bos, demand_bos, series1, series2, tf_label, tfbarindex, tfclose, atr, is_htf, swing_high, swing_low, swing_index) =>
    //      NEW SWING HIGH
    if not na(swing_high)

        //MANAGE SWING HIGH VALUES
        array_add_pop(pp_swing_high_values, swing_high)
        array_add_pop(pp_swing_high_bns, swing_index)

        support_resistance(pp_swing_high_values, pp_swing_high_bns, supply_zone, pp_current_supply_poi, current_supply_touch_count, 1, atr, tf_label, tfbarindex, is_htf)

    //      NEW SWING LOW
    else if not na(swing_low)

        //MANAGE SWING LOW VALUES
        array_add_pop(pp_swing_low_values, swing_low)
        array_add_pop(swing_low_bns, swing_index)
        
        support_resistance(pp_swing_low_values, swing_low_bns, demand_zone, pp_current_demand_poi, current_demand_touch_count, -1, atr, tf_label, tfbarindex, is_htf)


    sd_to_bos(supply_zone, supply_bos, current_supply_touch_count, pp_current_supply_poi, 1, tfclose, tfbarindex)
    sd_to_bos(demand_zone, demand_bos, current_demand_touch_count, pp_current_demand_poi, -1, tfclose, tfbarindex)

    extend_box_endpoint(supply_zone, current_supply_touch_count, tfbarindex, series1, series2)
    extend_box_endpoint(demand_zone, current_demand_touch_count, tfbarindex, series1, series2)

var pp_swing_high_values = array.new_float(5,0.00)
var pp_swing_low_values = array.new_float(5,0.00)

var pp_swing_high_bns = array.new_int(5,0)
var swing_low_bns = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi = array.new_box(pp_history_of_demand_to_keep, na)

var pp_supply_zones = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR BOS
var supply_bos = array.new_line(5, na)
var demand_bos = array.new_line(5, na)

var pp_swing_high_values1 = array.new_float(5,0.00)
var pp_swing_low_values1 = array.new_float(5,0.00)

var pp_swing_high_bns1 = array.new_int(5,0)
var swing_low_bns1 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones1 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones1 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply1 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand1 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi1 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi1 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos1 = array.new_line(5, na)
var demand_bos1 = array.new_line(5, na)

var pp_swing_high_values2 = array.new_float(5,0.00)
var pp_swing_low_values2 = array.new_float(5,0.00)

var pp_swing_high_bns2 = array.new_int(5,0)
var swing_low_bns2 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones2 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones2 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply2 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand2 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi2 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi2 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos2 = array.new_line(5, na)
var demand_bos2 = array.new_line(5, na)

var pp_swing_high_values3 = array.new_float(5,0.00)
var pp_swing_low_values3 = array.new_float(5,0.00)

var pp_swing_high_bns3 = array.new_int(5,0)
var swing_low_bns3 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones3 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones3 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply3 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand3 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi3 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi3 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos3 = array.new_line(5, na)
var demand_bos3 = array.new_line(5, na)

var pp_swing_high_values4 = array.new_float(5,0.00)
var pp_swing_low_values4 = array.new_float(5,0.00)

var pp_swing_high_bns4 = array.new_int(5,0)
var swing_low_bns4 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones4 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones4 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply4 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand4 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi4 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi4 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos4 = array.new_line(5, na)
var demand_bos4 = array.new_line(5, na)

var pp_swing_high_values5 = array.new_float(5,0.00)
var pp_swing_low_values5 = array.new_float(5,0.00)

var pp_swing_high_bns5 = array.new_int(5,0)
var swing_low_bns5 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones5 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones5 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply5 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand5 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi5 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi5 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos5 = array.new_line(5, na)
var demand_bos5 = array.new_line(5, na)

var pp_swing_high_values6 = array.new_float(5,0.00)
var pp_swing_low_values6 = array.new_float(5,0.00)

var pp_swing_high_bns6 = array.new_int(5,0)
var swing_low_bns6 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones6 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones6 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply6 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand6 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi6 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi6 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos6 = array.new_line(5, na)
var demand_bos6 = array.new_line(5, na)

var pp_swing_high_values7 = array.new_float(5,0.00)
var pp_swing_low_values7 = array.new_float(5,0.00)

var pp_swing_high_bns7 = array.new_int(5,0)
var swing_low_bns7 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones7 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones7 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply7 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand7 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi7 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi7 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos7 = array.new_line(5, na)
var demand_bos7 = array.new_line(5, na)

var pp_swing_high_values8 = array.new_float(5,0.00)
var pp_swing_low_values8 = array.new_float(5,0.00)

var pp_swing_high_bns8 = array.new_int(5,0)
var swing_low_bns8 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones8 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var demand_zones8 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var touches_for_supply8 = array.new_int(pp_history_of_demand_to_keep, 0)
var touches_for_demand8 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi8 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi8 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos8 = array.new_line(5, na)
var demand_bos8 = array.new_line(5, na)

var pp_swing_high_values9 = array.new_float(5,0.00)
var pp_swing_low_values9 = array.new_float(5,0.00)

var pp_swing_high_bns9 = array.new_int(5,0)
var swing_low_bns9 = array.new_int(5,0)

//      ARRAYS FOR SUPPORT / RESISTANCE
var pp_supply_zones9 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)
var pp_demand_zones9 = array.new<zone_with_strength>(pp_history_of_demand_to_keep, na)

var pp_touches_for_supply9 = array.new_int(pp_history_of_demand_to_keep, 0)
var pp_touches_for_demand9 = array.new_int(pp_history_of_demand_to_keep, 0)

//      ARRAYS FOR SUPPORT / RESISTANCE POI LABELS
var pp_current_supply_poi9 = array.new_box(pp_history_of_demand_to_keep, na)
var pp_current_demand_poi9 = array.new_box(pp_history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var pp_supply_bos9 = array.new_line(5, na)
var pp_demand_bos9 = array.new_line(5, na)

pp_atr = ta.atr(50)
if pp_show_current_sr
    cur_tf_label = timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period
    swing_high = ta.pivothigh(pp_swing_length, pp_swing_length)
    swing_low = ta.pivotlow(pp_swing_length, pp_swing_length)
    swing_index = bar_index[pp_swing_length]
    calculateSR(pp_swing_high_values, pp_swing_low_values, pp_swing_high_bns, swing_low_bns, pp_supply_zones, demand_zones, touches_for_supply, touches_for_demand, pp_current_supply_poi, pp_current_demand_poi, supply_bos, demand_bos, high, low, cur_tf_label, bar_index, close, pp_atr, false, swing_high, swing_low, swing_index)


// SUPPORT / RESISTANCE FOR HTF1
[high1, low1, close1, barindex1, atr1, timeframe1, swing_high1, swing_low1, swing_index1] = request.security(syminfo.tickerid, pp_htf1, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF2
[high2, low2, close2, barindex2, atr2, timeframe2, swing_high2, swing_low2, swing_index2] = request.security(syminfo.tickerid, pp_htf2, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF3
[high3, low3, close3, barindex3, atr3, timeframe3, swing_high3, swing_low3, swing_index3] = request.security(syminfo.tickerid, pp_htf3, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF4
[high4, low4, close4, barindex4, atr4, timeframe4, swing_high4, swing_low4, swing_index4] = request.security(syminfo.tickerid, pp_htf4, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF5
[high5, low5, close5, barindex5, atr5, timeframe5, swing_high5, swing_low5, swing_index5] = request.security(syminfo.tickerid, htf5, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF6
[high6, low6, close6, barindex6, atr6, timeframe6, swing_high6, swing_low6, swing_index6] = request.security(syminfo.tickerid, pp_htf6, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF7
[high7, low7, close7, barindex7, atr7, timeframe7, swing_high7, swing_low7, swing_index7] = request.security(syminfo.tickerid, pp_htf7, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF8
[high8, low8, close8, barindex8, atr8, timeframe8, swing_high8, swing_low8, swing_index8] = request.security(syminfo.tickerid, pp_htf8, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

// SUPPORT / RESISTANCE FOR HTF9
[high9, low9, close9, barindex9, atr9, timeframe9, swing_high9, swing_low9, swing_index9] = request.security(syminfo.tickerid, pp_htf9, [high, low, close, bar_index, ta.atr(50), timeframe.isintraday ? str.tostring(timeframe.in_seconds() / 60, "#.## MIN") : timeframe.period, ta.pivothigh(pp_swing_length, pp_swing_length), ta.pivotlow(pp_swing_length, pp_swing_length), bar_index[pp_swing_length]])

if pp_enableHtf1 and pp_htf1 != ""
    calculateSR(pp_swing_high_values1, pp_swing_low_values1, pp_swing_high_bns1, swing_low_bns1, pp_supply_zones1, demand_zones1, touches_for_supply1, touches_for_demand1, pp_current_supply_poi1, pp_current_demand_poi1, supply_bos1, demand_bos1, high1, low1, timeframe1, barindex1, close1, atr1, true, swing_high1, swing_low1, swing_index1)

if pp_enableHtf2 and pp_htf2 != ""
    calculateSR(pp_swing_high_values2, pp_swing_low_values2, pp_swing_high_bns2, swing_low_bns2, pp_supply_zones2, demand_zones2, touches_for_supply2, touches_for_demand2, pp_current_supply_poi2, pp_current_demand_poi2, supply_bos2, demand_bos2, high2, low2, timeframe2, barindex2, close2, atr2, true, swing_high2, swing_low2, swing_index2)

if pp_enableHtf3 and pp_htf3 != ""
    calculateSR(pp_swing_high_values3, pp_swing_low_values3, pp_swing_high_bns3, swing_low_bns3, pp_supply_zones3, demand_zones3, touches_for_supply3, touches_for_demand3, pp_current_supply_poi3, pp_current_demand_poi3, supply_bos3, demand_bos3, high3, low3, timeframe3, barindex3, close3, atr3, true, swing_high3, swing_low3, swing_index3)

if pp_enableHtf4 and pp_htf4 != ""
    calculateSR(pp_swing_high_values4, pp_swing_low_values4, pp_swing_high_bns4, swing_low_bns4, pp_supply_zones4, demand_zones4, touches_for_supply4, touches_for_demand4, pp_current_supply_poi4, pp_current_demand_poi4, supply_bos4, demand_bos4, high4, low4, timeframe4, barindex4, close4, atr4, true, swing_high4, swing_low4, swing_index4)

if pp_enableHtf5 and htf5 != ""
    calculateSR(pp_swing_high_values5, pp_swing_low_values5, pp_swing_high_bns5, swing_low_bns5, pp_supply_zones5, demand_zones5, touches_for_supply5, touches_for_demand5, pp_current_supply_poi5, pp_current_demand_poi5, supply_bos5, demand_bos5, high5, low5, timeframe5, barindex5, close5, atr5, true, swing_high5, swing_low5, swing_index5)

if pp_enableHtf6 and pp_htf6 != ""
    calculateSR(pp_swing_high_values6, pp_swing_low_values6, pp_swing_high_bns6, swing_low_bns6, pp_supply_zones6, demand_zones6, touches_for_supply6, touches_for_demand6, pp_current_supply_poi6, pp_current_demand_poi6, supply_bos6, demand_bos6, high6, low6, timeframe6, barindex6, close6, atr6, true, swing_high6, swing_low6, swing_index6)

if pp_enableHtf7 and pp_htf7 != ""
    calculateSR(pp_swing_high_values7, pp_swing_low_values7, pp_swing_high_bns7, swing_low_bns7, pp_supply_zones7, demand_zones7, touches_for_supply7, touches_for_demand7, pp_current_supply_poi7, pp_current_demand_poi7, supply_bos7, demand_bos7, high7, low7, timeframe7, barindex7, close7, atr7, true, swing_high7, swing_low7, swing_index7)

if pp_enableHtf8 and pp_htf8 != ""
    calculateSR(pp_swing_high_values8, pp_swing_low_values8, pp_swing_high_bns8, swing_low_bns8, pp_supply_zones8, demand_zones8, touches_for_supply8, touches_for_demand8, pp_current_supply_poi8, pp_current_demand_poi8, supply_bos8, demand_bos8, high8, low8, timeframe8, barindex8, close8, atr8, true, swing_high8, swing_low8, swing_index8)

if pp_enableHtf9 and pp_htf9 != ""
    calculateSR(pp_swing_high_values9, pp_swing_low_values9, pp_swing_high_bns9, swing_low_bns9, pp_supply_zones9, pp_demand_zones9, pp_touches_for_supply9, pp_touches_for_demand9, pp_current_supply_poi9, pp_current_demand_poi9, pp_supply_bos9, pp_demand_bos9, high9, low9, timeframe9, barindex9, close9, atr9, true, swing_high9, swing_low9, swing_index9)


//------ END Protein+ ------//

//------ START Swing Highs/Lows & Candle Patterns ------//

// indicator("Swing Highs/Lows & Candle Patterns [LuxAlgo]", "LuxAlgo - Swing Highs/Lows & Candle Patterns", overlay = true, max_labels_count = 500)
//------------------------------------------------------------------------------
//Settings
//-----------------------------------------------------------------------------{
shl_length = input(21)

//Style
shl_swinghCss = input(#b33939, 'Swing High', group = 'Swing Highs/Lows & Candle Patterns Settings')
shl_swinglCss = input(#218c74, 'Swing Low', group = 'Swing Highs/Lows & Candle Patterns Settings')

//-----------------------------------------------------------------------------}
//Descriptions
//-----------------------------------------------------------------------------{
shl_hammer_ = "The hammer candlestick pattern is formed of a short body with a long lower wick, and is found at the bottom of a downward trend."
  + "\n" + "\n A hammer shows that although there were selling pressures during the day, ultimately a strong buying pressure drove the price back up." 
shl_i_hammer_ = "The inverted hammer is a similar pattern than the hammer pattern. The only difference being that the upper wick is long, while the lower wick is short."
  + "\n" + "\n It indicates a buying pressure, followed by a selling pressure that was not strong enough to drive the market price down. The inverse hammer suggests that buyers will soon have control of the market."
shl_bulleng_ = "The bullish engulfing pattern is formed of two candlesticks. The first candle is a short red body that is completely engulfed by a larger green candle"
  + "\n" + "\n Though the second day opens lower than the first, the bullish market pushes the price up, culminating in an obvious win for buyers"
shl_hanging_ = "The hanging man is the bearish equivalent of a hammer; it has the same shape but forms at the end of an uptrend."
  + "\n" + "It indicates that there was a significant sell-off during the day, but that buyers were able to push the price up again. The large sell-off is often seen as an indication that the bulls are losing control of the market."
shl_shooting_ = "The shooting star is the same shape as the inverted hammer, but is formed in an uptrend: it has a small lower body, and a long upper wick."
  + "\n" + "Usually, the market will gap slightly higher on opening and rally to an intra-day high before closing at a price just above the open – like a star falling to the ground."
shl_beareng_ = "A bearish engulfing pattern occurs at the end of an uptrend. The first candle has a small green body that is engulfed by a subsequent long red candle."
  + "\n" + "It signifies a peak or slowdown of price movement, and is a sign of an impending market downturn. The lower the second candle goes, the more significant the trend is likely to be."

//-----------------------------------------------------------------------------}
//UDT
//-----------------------------------------------------------------------------{
type pattern
    bool condition
    string title
    string description
  
//-----------------------------------------------------------------------------}
//Data
//-----------------------------------------------------------------------------{
var float phy = na
var float ply = na

o = open[shl_length]
h = high[shl_length]
l = low[shl_length]
c = close[shl_length]

d = math.abs(c - o)
ph = ta.pivothigh(shl_length, shl_length)
pl = ta.pivotlow(shl_length, shl_length)

//-----------------------------------------------------------------------------{
//Patterns
//-----------------------------------------------------------------------------}
hammer   = pattern.new(pl and math.min(o,c) - l > d and h - math.max(c,o) < d, 'Hammer', shl_hammer_)
ihammer  = pattern.new(pl and h - math.max(c,o) > d and math.min(c,o) - l < d, 'Inverted Hammer', shl_i_hammer_)
bulleng  = pattern.new(c > o and c[1] < o[1] and c > o[1] and o < c[1], 'Bullish Engulfing', shl_bulleng_)
hanging  = pattern.new(ph and math.min(c,o) - l > d and h - math.max(o,c) < d, 'Hanging Man', shl_hanging_)
shooting = pattern.new(ph and h - math.max(o,c) > d and math.min(c,o) - l < d, 'Shooting Star', shl_shooting_)
beareng  = pattern.new(c > o and c[1] < o[1] and c > o[1] and o < c[1], 'Bearish Engulfing', shl_beareng_)

//-----------------------------------------------------------------------------}
//Set labels
//-----------------------------------------------------------------------------{
n = bar_index

pattern pattern_obj = hammer.condition ? hammer
  : ihammer.condition ? ihammer
  : bulleng.condition ? bulleng
  : hanging.condition ? hanging
  : shooting.condition ? shooting
  : beareng.condition ? beareng
  : na

if ph
    H = ph > phy ? 'HH' : 'LH'

    //Set label
    label.new(n[shl_length], ph
      , H + "\n" + (na(pattern_obj) ? 'None' : pattern_obj.title)
      , color = color(na)
      , style = label.style_label_down
      , textcolor = shl_swinghCss
      , tooltip = na(pattern_obj) ? '' : pattern_obj.description)
    
    phy := ph

else if pl
    L = pl < ply ? 'LL' : 'HL'

    //Set label
    label.new(n[shl_length], pl
      , L + "\n" + (na(pattern_obj) ? 'None' : pattern_obj.title)
      , color = color(na)
      , style = label.style_label_up
      , textcolor = shl_swinglCss
      , tooltip = na(pattern_obj) ? '' : pattern_obj.description)

    ply := pl

//-----------------------------------------------------------------------------}

//------ END Swing Highs/Lows & Candle Patterns ------//