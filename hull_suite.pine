//------ START Hull Suite ------//

//indicator('Hull Suite by InSilico', overlay=true)

//INPUT
hs_src = input(close, title='Source', group="HULL SUITE SETTINGS")
hs_modeSwitch = input.string('Hma', title='Hull Variation', options=['Hma', 'Thma', 'Ehma'], group="HULL SUITE SETTINGS")
hs_length = input(55, title='Length(180-200 for floating S/R , 55 for swing entry)', group="HULL SUITE SETTINGS")
hs_lengthMult = input(1.2, title='Length multiplier (Used to view higher timeframes with straight band)', group="HULL SUITE SETTINGS")

hs_useHtf = input(false, title='Show Hull MA from X timeframe? (good for scalping)', group="HULL SUITE SETTINGS")
hs_htf = input.timeframe('240', title='Higher timeframe', group="HULL SUITE SETTINGS")

hs_switchColor = input(true, 'Color Hull according to trend?', group="HULL SUITE SETTINGS")
hs_candleCol = input(false, title='Color candles based on Hull\'s Trend?', group="HULL SUITE SETTINGS")
hs_visualSwitch = input(true, title='Show as a Band?', group="HULL SUITE SETTINGS")
hs_thicknesSwitch = input(2, title='Line Thickness', group="HULL SUITE SETTINGS")
hs_transpSwitch = input.int(30, title='Band Transparency', step=5, group="HULL SUITE SETTINGS")

//FUNCTIONS
//HMA
HMA(_hs_src, _hs_length) =>
    ta.wma(2 * ta.wma(_hs_src, _hs_length / 2) - ta.wma(_hs_src, _hs_length), math.round(math.sqrt(_hs_length)))
//EHMA    
EHMA(_hs_src, _hs_length) =>
    ta.ema(2 * ta.ema(_hs_src, _hs_length / 2) - ta.ema(_hs_src, _hs_length), math.round(math.sqrt(_hs_length)))
//THMA    
THMA(_hs_src, _hs_length) =>
    ta.wma(ta.wma(_hs_src, _hs_length / 3) * 3 - ta.wma(_hs_src, _hs_length / 2) - ta.wma(_hs_src, _hs_length), _hs_length)

//SWITCH
Mode(hs_modeSwitch, hs_src, len) =>
    hs_modeSwitch == 'Hma' ? HMA(hs_src, len) : hs_modeSwitch == 'Ehma' ? EHMA(hs_src, len) : hs_modeSwitch == 'Thma' ? THMA(hs_src, len / 2) : na

//OUT
_hs_hull = Mode(hs_modeSwitch, hs_src, int(hs_length * hs_lengthMult))
hs_HULL = hs_useHtf ? request.security(syminfo.ticker, hs_htf, _hs_hull) : _hs_hull
hs_MHULL = hs_HULL[0]
hs_SHULL = hs_HULL[2]

//COLOR
hs_hullColor = hs_switchColor ? hs_HULL > hs_HULL[2] ? #218c74 : #b33939 : #cd6133

//PLOT
// < Frame
hs_Fi1 = plot(hs_MHULL, title='(HS) MHULL', color=hs_hullColor, linewidth=hs_thicknesSwitch)
hs_Fi2 = plot(hs_visualSwitch ? hs_SHULL : na, title='(HS) SHULL', color=hs_hullColor, linewidth=hs_thicknesSwitch)

// alertcondition(ta.crossover(hs_MHULL, hs_SHULL), title='Hull trending up.', message='Hull trending up.')
// alertcondition(ta.crossover(hs_SHULL, hs_MHULL), title='Hull trending down.', message='Hull trending down.')

///< Ending Filler
fill(hs_Fi1, hs_Fi2, title='Band Filler', color=color.new(hs_hullColor, hs_transpSwitch))
///BARCOLOR
barcolor(color=hs_candleCol ? hs_switchColor ? hs_hullColor : na : na, title="(HS) Bar Color")

//------ END Hull Suite ------//