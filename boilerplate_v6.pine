// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © fadizeidan

//@version=6
indicator("Pine Script Boilerplate Example", overlay = true)

// This is my boilerplate template for structuring code when writing an indicator.
// The introduction of types and methods marked a significant evolution in Pine Script capabilities,
// transitioning from a flat coding structure to an Object-Oriented structure. My coding
// structure heavily leverages these advancements.
// I typically organize my code in the following order:
// 1. Type declarations
// 2. Input settings
// 3. Variable declarations
// 4. Standard commonly used functions
// 5. Methods
// 6. Main execution logic
//
// Variables, functions, and methods need to be declared in order. If method A calls method B,
// then method B must be declared before method A.

///////////////////////////////////////////////////////////////////////////////////////////
// Types
///////////////////////////////////////////////////////////////////////////////////////////

// Types enable you to create your own "objects." I use them to logically group data,
// allowing for efficient storage of data sets (variables) in arrays, passing them to methods
// and functions, and quickly accessing related data sets.
// I prefer to start with input settings. Pine developers often have extensive lists of
// variables for input properties, which can become unmanageable. It becomes even more
// complicated when trying to track different values for the same setting, as I will explain below.

type Settings 
    bool    show
    string  htf
    int     max_display
    color   color
    string  style

// If I need to track different values for the same settings, I use a second type that
// you will see in my indicators. However, to keep it simple, I won't cover it here.
// For example:
//
// type TF_Setting
//     bool show
//     color bull_color
//     color bear_color

// Now comes the main objects that I use in my code. I want to track the OHLC of multiple
// timeframes (TF), so I create a TF type that will hold each candle's data:
// 1. open
// 2. high
// 3. low
// 4. close
// 5. open_time
// 6. close_time
//    I track when the timeframe started and closed. You can track it by time or bar_index.
//    bar_index is easier to work with. However, there is a limitation on how far back you can reference,
//    so I got used to using time instead. Sometimes I track both by having another attribute,
//    for example, open_index and close_index:
// 7. open_index
// 8. close_index
// 9. l_open
// 10. l_high
// 11. l_low
// 12. l_close
//    Along with the values, I track the objects I use to draw on the chart for easier cleaning.

type TF
    float open
    float high
    float low
    float close
    int   open_time
    int   close_time
    line  l_open
    line  l_high
    line  l_low
    line  l_close

///////////////////////////////////////////////////////////////////////////////////////////
// Input                                                                                 //
///////////////////////////////////////////////////////////////////////////////////////////

// While I do have a section for variable declarations, I need to create the settings
// variables first to collect the input. Always use `var` with variables that need to
// retain their values across multiple executions.

var Settings settings   = Settings.new()

settings.show           := input.bool(true, '', inline = 'TF')
settings.htf            := input.timeframe('240', '', inline = 'TF')
settings.color          := input.color(color.black, '', inline = 'TF')
settings.style          := input.string('⎯⎯⎯', '', options = ['⎯⎯⎯', '----', '····'], inline='TF')
settings.max_display    := input.int(5, 'Maximum candles to display', minval=1)


///////////////////////////////////////////////////////////////////////////////////////////
// variable declarations                                                                 //
///////////////////////////////////////////////////////////////////////////////////////////

// Since I need to track multiple candles, I need an array of TF objects. This approach
// allows me to track each candle as a unit.

var TF[] TF_levels      = array.new<TF>()

// a common variable I use is transparent color, added here as an example
var color color_transparent = #00000000


///////////////////////////////////////////////////////////////////////////////////////////
// functions                                                                             //
///////////////////////////////////////////////////////////////////////////////////////////

// I like to separate functions from methods because:
// A) They are often used in methods and need to be defined beforehand.
// B) This approach is cleaner, making it easier to locate and edit them when needed.
//
// LineStyle is a commonly used function in my code since I frequently draw lines. I am
// including it here as an example.

LineStyle(string style) =>
    switch style
        '⎯⎯⎯'  => line.style_solid
        '----' => line.style_dashed
        '····' => line.style_dotted

///////////////////////////////////////////////////////////////////////////////////////////
// methods                                                                               //
///////////////////////////////////////////////////////////////////////////////////////////

// Methods are special functions that you can "attach" to objects, similar to
// built-in functions. They work on almost any object/variable type in Pine Script.
// For example, if you want to add an Add function to an integer, you can do:
//
// method Add(int this, int value) =>
//    this + value
//
// In the example above, we extend the int type to include an Add function.
// The keyword method instructs Pine Script to attach the function "Add" to any variable
// declared as an int.
// The first parameter is always the type you want to attach the function to. You can name it
// however you like. Once the method is declared, you can call it on any integer variable.
//
// You do not pass the first parameter; it is derived from the object itself.
//
// var int values = 6
// var int other = 12
//
// var newval = values.Add(5)
// other := other.Add(18)
//
// I usually have the following methods. The naming may change, and I may have other methods, but
// this is the general approach.

// An Add method on the main collection to include a new set of values
method Add(TF[] this, float o, float h, float l, float c) =>
    // First create a new object to collect the data into. This will represent a new set
    // for the timeframe
    TF tf = TF.new()
    tf.open             := o
    tf.high             := h
    tf.low              := l
    tf.close            := c
    tf.open_time        := time
    tf.close_time       := time
    // then add it to the beginning of the collection/array
    this.unshift(tf)

    // if we are above the maximum size, remove the last instance and clean up existing drawings
    if this.size() > settings.max_display
        TF temp = this.pop()
        temp.l_open.delete()
        temp.l_high.delete()
    this

// An update function, to retrieve the first entry to modify. the logic is simple:
// when a new timeframe candle starts, this script will create a new set of data
// using the Add method and add it as first item in the collection/array.
//
// the update method will always update ghe first entry, which will always represent
// the latest timeframe candle.
method Update(TF[] this, float h, float l, float c, int close_time) =>
    if this.size() > 0
        TF tf           = this.first()
        tf.high         := high > tf.high ? high : tf.high
        tf.low          := low < tf.low   ? low  : tf.low
        tf.close        := close
        tf.close_time   := close_time

    this

// Render method is used to draw and update the lines on the chart.
// I usually use a render method, or similar, to have one location for my rendering
// logic. I only call it when data finished loading as shown in main logic.
method Render(TF[] this) =>
    for tf in this
        // if we have not created the lones yet, create them.
        if na(tf.l_open)
            tf.l_open   := line.new(tf.open_time, tf.open, tf.close_time, tf.open, xloc = xloc.bar_time, color = settings.color, style = LineStyle(settings.style))
            tf.l_high   := line.new(tf.open_time, tf.high, tf.close_time, tf.high, xloc = xloc.bar_time, color = settings.color, style = LineStyle(settings.style))
            tf.l_low    := line.new(tf.open_time, tf.low, tf.close_time, tf.low, xloc = xloc.bar_time, color = settings.color, style = LineStyle(settings.style))
            tf.l_close := line.new(tf.open_time, tf.close, tf.close_time, tf.close, xloc = xloc.bar_time, color = settings.color, style = LineStyle(settings.style))
        else
            //otherwise update them
            tf.l_open.set_x2(tf.close_time)
            tf.l_high.set_y1(tf.high)
            tf.l_low.set_y1(tf.low)
            tf.l_close.set_y1(tf.close)
            tf.l_high.set_xy2(tf.close_time, tf.high)
            tf.l_low.set_xy2(tf.close_time, tf.low)
            tf.l_close.set_xy2(tf.close_time, tf.close)
    this


///////////////////////////////////////////////////////////////////////////////////////////
// Main logic                                                                            //
///////////////////////////////////////////////////////////////////////////////////////////

// With the settings, functions, and methods declared, we can now focus on the main logic.
// If a new higher timeframe time changed (new candle started), we will call the Add method.
// otherwise we have new data for the current higher timeframe candle to Update.

if ta.change(time(settings.htf))>0
    TF_levels.Add(open, high, low, close)
else
    TF_levels.Update(high, low, close, time)

// Render the lines once the chart loading has reached the last bar. after that, it will be
// called on every price or volume change.
if barstate.islast
    TF_levels.Render()
