//@version=5
indicator("Stochastic RSI Buy/Sell Signal", overlay=true)

srsi_lengthRSI = input(14, title="RSI Length")
srsi_lengthStoch = input(14, title="Stochastic Length")
srsi_smoothK = input(3, title="Stochastic %K")
srsi_smoothD = input(3, title="Stochastic %D")
srsi_overboughtLevel = input(90, title="Overbought Level")
srsi_oversoldLevel = input(10, title="Oversold Level")
srsi_neutrallevel = input(50, title="Neutral Level")

srsi_rsiValue = ta.rsi(close, srsi_lengthRSI)
srsi_stochRsi = ta.stoch(srsi_rsiValue, srsi_rsiValue, srsi_rsiValue, srsi_lengthStoch)
srsi_k = ta.sma(srsi_stochRsi, srsi_smoothK)
srsi_d = ta.sma(srsi_k, srsi_smoothD)

// Buy Condition
srsi_buyCondition = srsi_k < srsi_oversoldLevel

// Sell Condition
srsi_sellCondition = srsi_k > srsi_overboughtLevel

// Plot Buy Signal
plotshape(series=srsi_buyCondition, title="SRSI", location=location.belowbar, color=color.new(color.green, 0), style=shape.circle, text="")

// Plot Sell Signal
plotshape(series=srsi_sellCondition, title="SRSI", location=location.abovebar, color=color.new(color.red, 0), style=shape.circle, text="")
