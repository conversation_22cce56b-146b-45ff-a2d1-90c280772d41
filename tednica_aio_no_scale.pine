//@version=5
indicator('TEDnica AIO No Scale', 'TEDnica AIO No Scale', true, scale = scale.none)

//------ START Market Sentiment Technicals ------//

//indicator('Market Sentiment Technicals [LuxAlgo]', 'LuxAlgo - Market Sentiment Technicals', true, scale = scale.none)

//---------------------------------------------------------------------------------------------------------------------
// Settings 
//---------------------------------------------------------------------------------------------------------------------{

mst_display  = display.all - display.status_line

mst_genericGroup = 'Market Sentiment Technicals Settings'

mst_tfTTip   = 'This option is utilized to calculate technical indicators for higher timeframes. If a timeframe lower than the chart\'s timeframe is selected, calculations will be based on the chart\'s timeframe.'
mst_indi_tf  = input.string('Chart', 'Timeframe', options = ['Chart', '5 Minutes', '15 Minutes', '1 Hour', '4 Hours', '1 Day', '1 Week', '1 Month'], group = mst_genericGroup, display = mst_display, tooltip = mst_tfTTip)
mst_hOffset  = input.int(50, 'Horizontal Offset', minval = 10, maxval = 360, group = mst_genericGroup, display = mst_display)

mst_bullColor = input.color(#0652DD, 'Gradient Colors: Bullish', inline = 'CSS', group = mst_genericGroup)
mst_bearColor = input.color(#EA2027, 'Bearish', inline = 'CSS', group = mst_genericGroup)

mst_panel_Group  = 'Indicators Sentiment Panel'
mst_panel_Show   = input.bool(true, 'Indicators Sentiment Panel', group = mst_genericGroup)
mst_panel_Height = input.int(3, '  Panel Height', minval = 2, maxval = 7, group = mst_genericGroup, display = mst_display)

mst_mrktMtr_Show = input.bool(true, 'Market Sentiment Meter', group = mst_genericGroup)

mst_mrktOsc_Group      = 'Market Sentiment Oscillator'
mst_mrktOsc_Show       = input.bool(false, 'Market Sentiment Oscillator', group = mst_genericGroup)
mst_mrktOsc_Divergence = input.string('None', "  Oscillator Show Divergence", options = ['Regular', 'Hidden', 'All', 'None'], group = mst_genericGroup, display = mst_display)
mst_mrktOsc_lnWidth    = input.int(1, '  Oscillator Line Width', minval = 1, group = mst_genericGroup, display = mst_display)
mst_mrktOsc_height     = input.float(3.5, '  Oscillator Height',  minval = 0, step = .5, group = mst_genericGroup, display = mst_display)

mst_rsi_Group  = 'Relative Strength Index Settings'
mst_rsi_Source = input.source(close, '  RSI Source', group = mst_genericGroup, display = mst_display)
mst_rsi_Length = input.int(14, '  RSI Length', minval = 1, group = mst_genericGroup, display = mst_display)

mst_stoch_Group   = 'Stochastic %K Settings'
mst_stoch_LengthK = input.int(14, '  Stochastic %K Stochastic %K Length', minval = 1, group = mst_genericGroup, display = mst_display)
mst_stoch_SmoothK = input.int(3, '  Stochastic %K %K Smoothing', minval = 1, group = mst_genericGroup, display = mst_display)

mst_stochRSI_Group     = 'Stochastic RSI Fast Settings'
mst_stochRSI_LengthK   = input.int(14, '  Stochastic RSI Fast Length', minval = 1, group = mst_genericGroup, display = mst_display)
mst_stochRSI_SmoothK   = input.int(3 , '  Stochastic RSI Fast %K Smoothing', minval = 1, group = mst_genericGroup, display = mst_display)
mst_stochRSI_SourceRSI = input.source(close, '  Stochastic RSI Fast RSI Source', group = mst_genericGroup, display = mst_display)
mst_stochRSI_LengthRSI = input.int(14, '  Stochastic RSI Fast RSI Length', minval = 1, group = mst_genericGroup, display = mst_display)

mst_cci_Group  = 'Commodity Channel Index Settings'
mst_cci_Source = input.source(hlc3, '  Commodity Channel Index Source', group = mst_genericGroup, display = mst_display)
mst_cci_Length = input.int(20, '  Commodity Channel Index Length', minval = 1, group = mst_genericGroup, display = mst_display)

mst_bbp_Group  = 'Bull Bear Power Settings'
mst_bbp_Length = input.int(13, '  Bull Bear Power Length', minval = 1, group = mst_genericGroup, display = mst_display)

mst_ma_Group  = 'MA Settings'
mst_maType    = input.string("SMA", "  MA Type", options = ["SMA", "EMA", "HMA", "RMA", "WMA", "VWMA"], group = mst_genericGroup, display = mst_display)
mst_maLength  = input.int(20, '  MA Length', minval = 1, group = mst_genericGroup, display = mst_display)

mst_vwap_Group = 'VWAP Settings'
mst_vwapAnchor = input.string('Auto', '  VWAP Anchor', options = ['Auto', 'Day', 'Week', 'Month', 'Quarter', 'Year'], group = mst_genericGroup, display = mst_display)
mst_vwapStDev  = input.float(2, '  VWAP StdDev', minval = 1, group = mst_genericGroup, display = mst_display)

mst_bb_Group  = 'Bollinger Bands Settings'
mst_bb_Type   = input.string("SMA", "  Bollinger Bands Basis Type", options = ["SMA", "EMA", "HMA", "RMA", "WMA", "VWMA"], group = mst_genericGroup, display = mst_display)
mst_bb_Source = input.source(close, "  Bollinger Bands Source", group = mst_genericGroup, display = mst_display)
mst_bb_Length = input.int(20, "  Bollinger Bands Length", minval = 1, group = mst_genericGroup, display = mst_display)
mst_bb_Mult   = input.float(2.0, "  Bollinger Bands StdDev", minval=0.01, maxval=50, group = mst_genericGroup, display = mst_display)

mst_st_Group  = 'Supertrend Settings'
mst_st_Period = input.int(10, '  Supertrend ATR Length', minval=1, group = mst_genericGroup, display = mst_display)
mst_st_Factor = input.float(3, '  Supertrend Factor', minval = 2, step = 0.1, group = mst_genericGroup, display = mst_display)

mst_lr_Group  = 'Linear Regression Settings'
mst_lr_Source = input.source(close, "  Linear Regression Source", group = mst_genericGroup, display = mst_display)
mst_lr_Length = input.int(25, "  Linear Regression Length", minval = 1, group = mst_genericGroup, display = mst_display)

mst_ms_Group  = 'Market Structure Settings'
mst_ms_Length = input.int(5, "  Market Structure Length", minval = 1, group = mst_genericGroup, display = mst_display)

mst_norm_Group  = 'Normalization Settings - Trend Indicators'
mst_norm_Smooth = input.int(3, '  Normalization Smoothing', minval = 1, group = mst_genericGroup, display = mst_display)

//---------------------------------------------------------------------------------------------------------------------}
// User Defined Types
//---------------------------------------------------------------------------------------------------------------------{

type panel
    box         []  tiValues
    label       []  tiTitles

//---------------------------------------------------------------------------------------------------------------------}
// Variables
//---------------------------------------------------------------------------------------------------------------------{

var mst_meterLines = array.new_line()
mst_circulus = array.new<chart.point>(0)
var mst_meterLabels = array.new_label()

//---------------------------------------------------------------------------------------------------------------------}
// Functions / Methods
//---------------------------------------------------------------------------------------------------------------------{

timeframe(timeframe) =>
    float chartTFinM = timeframe.in_seconds() / 60

    switch 
        timeframe == "5 Minutes"  and chartTFinM <= 5   => '5'
        timeframe == "15 Minutes" and chartTFinM <= 15  => '15'
        timeframe == "1 Hour"     and chartTFinM <= 60  => '60'
        timeframe == "4 Hours"    and chartTFinM <= 240 => '240'
        timeframe == "1 Day"      and timeframe.isintraday => 'D'
        timeframe == "1 Week"     and (timeframe.isdaily or timeframe.isintraday) => 'W'
        timeframe == "1 Month"    and (timeframe.isweekly or timeframe.isdaily or timeframe.isintraday) => 'M'
        => timeframe.period

timeframeText(timeframe) =>
    timeframeX = timeframe(timeframe)

    if not str.contains(timeframeX, "D") and not str.contains(timeframeX, "W") and not str.contains(timeframeX, "M") and not str.contains(timeframeX, "S")
        TFinNUM = str.tonumber(timeframeX)
        if TFinNUM < 60
            timeframeX + 'm'
        else
            str.tostring(TFinNUM / 60) + 'H'
    else
        timeframeX

autoAnchor(anchor) =>
    if anchor == 'Auto'
        if timeframe.isintraday
            timeframe.multiplier <= 15 ? 'D' : 'W'
        else if timeframe.isdaily
            'M'
        else if timeframe.isweekly
            '3M'
        else if timeframe.ismonthly
            '12M'
    else if anchor == 'Day'
        'D'
    else if anchor == 'Week'
        'W'
    else if anchor == 'Month'
        'M'
    else if anchor == 'Quarter'
        '3M'
    else if anchor == 'Year'
        '12M'

syminfo(symbol) =>
    symbol != '' ? str.substring(ticker.standard(symbol), str.pos(ticker.standard(symbol), ":") + 1) : syminfo.ticker

interpolate(value, valueHigh, valueLow, rangeHigh, rangeLow) =>
    rangeLow + (value - valueLow) * (rangeHigh - rangeLow) / (valueHigh - valueLow)

normalize(buy, sell, smooth)=>
    var os = 0
    var float max = na
    var float min = na
    os := buy ? 1 : sell ? -1 : os
    
    max := os > os[1] ? close : os < os[1] ? max : math.max(close, max)
    min := os < os[1] ? close : os > os[1] ? min : math.min(close, min)

    ta.sma((close - min)/(max - min), smooth) * 100

movingAverageValue(source, length, mst_maType) => 
    switch mst_maType
        "SMA"  => ta.sma (source, length)
        "EMA"  => ta.ema (source, length)
        "HMA"  => ta.hma (source, length)
        "RMA"  => ta.rma (source, length)
        "WMA"  => ta.wma (source, length)
        "VWMA" => ta.vwma(source, length)
        
rsi(source, length) =>
    rsi = ta.rsi(source, length)

    switch
        rsi > 70 => interpolate(rsi, 100, 70, 100, 75)
        rsi > 50 => interpolate(rsi, 70 , 50, 75 , 50)
        rsi > 30 => interpolate(rsi, 50 , 30, 50 , 25)
        rsi >= 0 => interpolate(rsi, 30 ,  0, 25 ,  0)

stochastic(lengthK, smoothK) =>
    stoch = ta.sma(ta.stoch(close, high, low, lengthK), smoothK)

    switch
        stoch > 80 => interpolate(stoch, 100, 80, 100, 75)
        stoch > 50 => interpolate(stoch, 80 , 50, 75 , 50)
        stoch > 20 => interpolate(stoch, 50 , 20, 50 , 25)
        stoch >= 0 => interpolate(stoch, 20 ,  0, 25 ,  0)

stochastic(rsiSource, rsiLength, stochLengthK, stochSmoothK) =>
    rsi = ta.rsi(rsiSource, rsiLength)
    stoch = ta.sma(ta.stoch(rsi, rsi, rsi, stochLengthK), stochSmoothK)

    switch
        stoch > 80 => interpolate(stoch, 100, 80, 100, 75)
        stoch > 50 => interpolate(stoch, 80 , 50, 75 , 50)
        stoch > 20 => interpolate(stoch, 50 , 20, 50 , 25)
        stoch >= 0 => interpolate(stoch, 20 ,  0, 25 ,  0)

cci(source, length) =>
    ma = ta.sma(source, length)
    cci = (source - ma) / (0.015 * ta.dev(source, length))

    switch
        cci > 100  => cci > 300 ? 100 : interpolate(cci, 300, 100, 100, 75)
        cci >= 0   => interpolate(cci, 100, 0, 75, 50)
        cci < -100 => cci < -300 ? 0 :  interpolate(cci, -100, -300, 25, 0)
        cci < 0    => interpolate(cci, 0, -100, 50, 25)

bullBearPower(length) => 
    bbp = high + low - 2 * movingAverageValue(close, length, 'EMA')
    [_, upper, lower] = ta.bb(bbp, 100, 2)

    switch
        bbp > upper => bbp > 1.5 * upper ? 100 : interpolate(bbp, 1.5 * upper, upper, 100, 75)
        bbp > 0 => interpolate(bbp, upper, 0, 75 , 50)
        bbp < lower => bbp < 1.5 * lower ? 0 : interpolate(bbp, lower, 1.5 * lower, 25, 0) 
        bbp < 0 => interpolate(bbp, 0, lower, 50 , 25)

movingAverage(source, length, mst_maType) => 
    basis = movingAverageValue(source, length, mst_maType)

    normalize(close > basis, close < basis, mst_norm_Smooth)

bollingerBands(source, length, multiplier, mst_maType) =>
    basis = movingAverageValue(source, length, mst_maType)
    deviation = multiplier * ta.stdev(source, length)

    normalize(close > basis + deviation, close < basis - deviation, mst_norm_Smooth)

supertrend(factor, period) =>
    [supertrend1, direction1] = ta.supertrend(factor, period)

    normalize(close > supertrend1, close < supertrend1, mst_norm_Smooth)

vwapBands(source, anchor, stdev)=>
    [_, upper, lower] = ta.vwap(source, timeframe.change(autoAnchor(anchor)), stdev)

    normalize(close > upper, close < lower, mst_norm_Smooth)

linearRegression(source, length) =>
    50 * ta.correlation(source, bar_index, length) + 50

marketStructure(length) => 
    var float ph_y = na
    var float pl_y = na
    var ph_cross = false, var pl_cross = false

    bull = false
    bear = false

    ph = ta.pivothigh(length, length)
    pl = ta.pivotlow (length, length)

    if not na(ph)
        ph_y := ph
        ph_cross := false

    if not na(pl) 
        pl_y := pl
        pl_cross := false

    if close > ph_y and not ph_cross
        ph_cross := true
        bull := true

    if close < pl_y and not pl_cross
        pl_cross := true
        bear := true

    normalize(bull, bear, mst_norm_Smooth)


collectData(timeframe) =>

    request.security(syminfo.tickerid, timeframe, 
         [close,
         rsi(mst_rsi_Source, mst_rsi_Length), 
         stochastic(mst_stoch_LengthK, mst_stoch_SmoothK),
         stochastic(mst_stochRSI_SourceRSI, mst_stochRSI_LengthRSI, mst_stochRSI_LengthK, mst_stochRSI_SmoothK), 
         cci(mst_cci_Source, mst_cci_Length),
         bullBearPower(mst_bbp_Length),
         movingAverage(close, mst_maLength, mst_maType),
         vwapBands(close, mst_vwapAnchor, mst_vwapStDev),
         bollingerBands(mst_bb_Source, mst_bb_Length, mst_bb_Mult, mst_bb_Type),
         supertrend(mst_st_Factor, mst_st_Period),
         linearRegression(mst_lr_Source, mst_lr_Length),
         marketStructure(mst_ms_Length)
     ])


processData(show, timeframe, closeValue, rsiValue, stochValue, stochRSIValue, cciValue, bbpValue, maValue, vwapValue, bbValue, stValue, regValue, msValue, sentiment, offset, size) => 

    if show
        var panel tiPanel = 
             panel.new(
                 array.new<box>(na), 
                 array.new<label>(na)
             )

        if tiPanel.tiValues.size() > 0
            for i = 0 to tiPanel.tiValues.size() - 1
                box.delete(tiPanel.tiValues.shift())

        if tiPanel.tiTitles.size() > 0
            for i = 0 to tiPanel.tiTitles.size() - 1
                label.delete(tiPanel.tiTitles.shift())

        oscIndies = array.from(rsiValue, stochValue, stochRSIValue, cciValue, bbpValue)
        oscNames  = array.from('R\nS\nI', '%\nK', 'S\nT\nR\nS\nI', 'C\nC\nI', 'B\nB\nP')
        oscTitles = array.from('Relative Strength Index (RSI)', 'Stochastic %K', 'Stochastic RSI %K', 'Commodity Channel Index', 'Bull Bear Power')

        trendIndies = array.from(maValue, vwapValue, bbValue, stValue, regValue, msValue)
        trendNames  = array.from('M\nA', 'V\nW\nA\nP', 'B\nB', 'S\nT', 'R\nE\nG', 'M\nS')
        trendTitles = array.from(str.format('Moving Average ({0} {1})', mst_maType, mst_maLength), 'Volume Weighted Average Price', 'Bollinger Bands', 'Supertrend', 'Linear Regression', 'Market Structure')

        pSize = oscIndies.size() + trendIndies.size()

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index + int((3 * (pSize + 1) - 1) / 2), size + offset + .5, 
             syminfo(syminfo.tickerid) + ' (' + str.tostring(closeValue) + ') · ' + timeframeText(timeframe), 
             color = color(na), style = label.style_label_down, textcolor = chart.fg_color))

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index, interpolate(75, 100, 0, size, 0) + offset, 'Overbought', 
             color = color(na), style = label.style_label_right, textcolor = mst_bullColor))

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index, interpolate(50, 100, 0, size, 0) + offset, 'Neutral', 
             color = color(na), style = label.style_label_right, textcolor = #787b86))

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index, interpolate(25, 100, 0, size, 0) + offset, 'Oversold', 
             color = color(na), style = label.style_label_right, textcolor = mst_bearColor))

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index + 3 * (pSize + 1), interpolate(75, 100, 0, size, 0) + offset, 'Strong Bullish Trend', 
             color = color(na), style = label.style_label_left, textcolor = mst_bullColor))

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index + 3 * (pSize + 1), interpolate(50, 100, 0, size, 0) + offset, 'Trendless', 
             color = color(na), style = label.style_label_left, textcolor = #787b86))

        tiPanel.tiTitles.push(label.new(
             mst_hOffset + bar_index + 3 * (pSize + 1), interpolate(25, 100, 0, size, 0) + offset, 'Strong Bearish Trend', 
             color = color(na), style = label.style_label_left, textcolor = mst_bearColor))

        tiPanel.tiValues.push(box.new(
             mst_hOffset + bar_index - 1 , offset + interpolate(75, 100, 0, size, 0), 
             mst_hOffset + bar_index + 3 * (pSize + 1) , offset + interpolate(75, 100, 0, size, 0),
             color.new(mst_bullColor, 50), bgcolor = mst_bullColor ))

        tiPanel.tiValues.push(box.new(
             mst_hOffset + bar_index - 1 , offset + interpolate(50, 100, 0, size, 0), 
             mst_hOffset + bar_index + 3 * (pSize + 1), offset + interpolate(50, 100, 0, size, 0),
             color.new(#787b86, 50), bgcolor = #787b86 ))

        tiPanel.tiValues.push(box.new(
             mst_hOffset + bar_index - 1 , offset + interpolate(25, 100, 0, size, 0), 
             mst_hOffset + bar_index + 3 * (pSize + 1), offset + interpolate(25, 100, 0, size, 0),
             color.new(mst_bearColor, 50), bgcolor = mst_bearColor ))

        tiPanel.tiValues.push(box.new(
             mst_hOffset + bar_index                      , offset + size + .50, 
             mst_hOffset + bar_index + 3 * (pSize + 1) - 1, offset + size + .25,
             color.new(#787b86, 73), bgcolor = color.new(#787b86, 83) ))

        tiPanel.tiValues.push(box.new(
             mst_hOffset + bar_index, offset + size + .50, 
             mst_hOffset + bar_index + math.round((3 * (pSize + 1) - 1) * sentiment / 100), offset + size + .25,
             color.new(chart.fg_color, 73), bgcolor = color.from_gradient(sentiment, 0, 100, mst_bearColor, mst_bullColor) ))

        tiPanel.tiValues.push(box.new(
             mst_hOffset + bar_index + 3 + 3 * (oscIndies.size()) - 2, offset + size * .85, 
             mst_hOffset + bar_index + 3 + 3 * (oscIndies.size()) - 2, offset + size * -.2,
             color.new(#787b86, 75), bgcolor = color(na) ))

        for [index, element] in oscIndies

            tiPanel.tiValues.push(box.new(
                 mst_hOffset + bar_index + 3 * index    , offset + interpolate(size * .5, 1, 0, 1, 0), // size * .5
                 mst_hOffset + bar_index + 3 * index + 2, offset + interpolate(size * element, 100, 0, 1, 0),// size * (element - 0) / 100, 
                 color(na), bgcolor = color.from_gradient(element, 0, 100, mst_bearColor, mst_bullColor) ))

            tiPanel.tiTitles.push(label.new(
                 mst_hOffset + bar_index + 3 * index + 1, offset, oscNames.get(index), color = color(na), size = size.small, style = label.style_label_up, 
                 textcolor = chart.fg_color, tooltip = oscTitles.get(index) + '\n Calculated Score: ' + str.tostring(element, '#.##') ))


        for [index, element] in trendIndies

            tiPanel.tiValues.push(box.new(
                 mst_hOffset + bar_index + 3 + 3 * (oscIndies.size() + index)    , offset + size * .5, 
                 mst_hOffset + bar_index + 3 + 3 * (oscIndies.size() + index) + 2, offset + size * (element - 0) / 100,
                 color(na), bgcolor = color.from_gradient(element, 0, 100, mst_bearColor, mst_bullColor) ))

            tiPanel.tiTitles.push(label.new(
                 mst_hOffset + bar_index + 3 + 3 * (oscIndies.size() + index) + 1, offset, trendNames.get(index), color = color(na), size = size.tiny, style = label.style_label_up, 
                 textcolor = chart.fg_color, tooltip = trendTitles.get(index) + '\n Calculated Score: ' + str.tostring(element, '#.##') ))

scoreLeft(value) =>
    switch
        value >= 48.5 => 13
        value >= 45.3 => 12
        value >= 45 => 11
        value >= 42 => 10
        value >= 38.63 =>  9
        value >= 35.2 =>  8
        value >= 33.3 =>  7
        value >= 31.5 =>  6
        value >= 27.5 =>  5
        value >= 23.1 =>  4
        value >= 20 =>  3
        value >= 15.5 =>  2
        value >=  0 =>  1

scoreRight(value) =>
    switch
        value < 51.6 => 0
        value < 54.75 => 1
        value < 55.5 => 2
        value < 58.1 => 3
        value < 61.39 => 4
        value < 64.8 => 5
        value < 66.5 => 6
        value < 68.6 => 7
        value < 72.53 => 8
        value < 76.3 => 9
        value < 78.8 => 10
        value < 82.5 => 11
        value < 90 => 12
        value <= 100 => 13

processMeter(show, sentiment) =>
    if show
        radius = 13
        coefficient = .15
        base = 1.1
        index = bar_index 
        degrees = -(sentiment - 100) * 9 / 5
        
        polylines = polyline.all
        if polylines.size() > 0
            for i = 0 to polylines.size() - 1
                polyline.delete(polylines.shift())

        if mst_meterLines.size() > 0
            for i = 0 to mst_meterLines.size() - 1
                line.delete(mst_meterLines.shift())

        if mst_meterLabels.size() > 0
            for i = 0 to mst_meterLabels.size() - 1
                label.delete(mst_meterLabels.shift())

        //log.info("yaz_kizim {0} {1}", sentiment, degrees)

        for segment = radius to 0
            mst_circulus.push(chart.point.from_index(index - segment +  mst_hOffset + 17, base + coefficient * math.sqrt(math.pow(radius, 2) - math.pow(segment, 2)) ))

            if sentiment >= 50 or radius - segment <= scoreLeft(sentiment)
                mst_meterLines.push(line.new(
                     index - segment + mst_hOffset + 17, base + coefficient * math.sqrt(math.pow(radius, 2) - math.pow(segment    , 2)), 
                     index - segment + mst_hOffset + 16, base + coefficient * math.sqrt(math.pow(radius, 2) - math.pow(segment + 1, 2)), 
                     xloc.bar_index, extend.none, color.from_gradient(radius - segment, 0, 26, mst_bearColor, mst_bullColor), line.style_solid, 5))

        for segment = radius to 0
            mst_circulus.push(chart.point.from_index(index + radius - segment + mst_hOffset + 17, 
                                             base + coefficient * math.sqrt(math.pow(radius, 2) - math.pow(radius - segment, 2)) ))

            if sentiment > 50 and radius - segment < scoreRight(sentiment) 
                mst_meterLines.push(line.new(
                     index - segment + radius + mst_hOffset + 17, base + coefficient * math.sqrt(math.pow(radius, 2) - math.pow(radius - segment    , 2)), 
                     index - segment + radius + mst_hOffset + 18, base + coefficient * math.sqrt(math.pow(radius, 2) - math.pow(radius - segment + 1, 2)), 
                     xloc.bar_index, extend.none, color.from_gradient(2 * radius - segment - 1, 0, 26, mst_bearColor, mst_bullColor), line.style_solid, 5))

        polyline.new(mst_circulus, false, false, line_color =  color.new(chart.fg_color, 73), line_width = 5)

        mst_meterLines.push(line.new(
             index + mst_hOffset + 17, base + coefficient,  
             index + mst_hOffset + 17 + math.round((radius - 3) * math.cos(math.toradians(degrees))), base + coefficient * ((radius - 3) * math.sin(math.toradians(degrees))), 
             xloc.bar_index, extend.none, color.new(chart.fg_color, 50), line.style_solid, 3))

        mst_meterLines.push(line.new(
             index + mst_hOffset + 17, base + coefficient,  
             index + mst_hOffset + 17 + math.round((radius - 3) * math.cos(math.toradians(degrees))), base + coefficient * ((radius - 3) * math.sin(math.toradians(degrees))), 
             xloc.bar_index, extend.none, color.new(chart.fg_color, 15), line.style_arrow_right, 1))

        mst_meterLabels.push(label.new(index + mst_hOffset + 17, base + coefficient, '', 
             color = color.new(chart.fg_color, 15), style = label.style_circle, size = size.auto, tooltip = str.tostring(sentiment, '#.#')))

        mst_meterLabels.push(label.new(index + mst_hOffset + 17 + math.round(radius * math.cos(math.toradians(160))), 
             base + coefficient * (radius * math.sin(math.toradians(160))), 'Strong\nBearish', 
             color = color(na), style = label.style_label_right, textcolor = sentiment <= 20 ? mst_bearColor : #787b86))

        mst_meterLabels.push(label.new(index + mst_hOffset + 17 + math.round(radius * math.cos(math.toradians(130))), 
             base + coefficient * (radius * math.sin(math.toradians(130))), 'Bearish', 
             color = color(na), style = label.style_label_lower_right, textcolor = sentiment > 20 and sentiment <= 40 ? mst_bearColor : #787b86))

        mst_meterLabels.push(label.new(index + mst_hOffset + 17 + math.round(radius * math.cos(math.toradians( 90))), 
             base + coefficient * (radius * math.sin(math.toradians( 90))), 'Neutral', 
             color = color(na), style = label.style_label_down, textcolor = sentiment > 40 and sentiment <= 60 ? chart.fg_color : #787b86))

        mst_meterLabels.push(label.new(index + mst_hOffset + 17 + math.round(radius * math.cos(math.toradians( 50))), 
             base + coefficient * (radius * math.sin(math.toradians( 50))), 'Bullish', 
             color = color(na), style = label.style_label_lower_left, textcolor = sentiment > 60 and sentiment <= 80 ? mst_bullColor : #787b86))

        mst_meterLabels.push(label.new(index + mst_hOffset + 17 + math.round(radius * math.cos(math.toradians( 20))), 
             base + coefficient * (radius * math.sin(math.toradians( 20))), 'Strong\nBullish', 
             color = color(na), style = label.style_label_left, textcolor = sentiment > 80 ? mst_bullColor : #787b86))

//---------------------------------------------------------------------------------------------------------------------}
// Calculations
//---------------------------------------------------------------------------------------------------------------------{

[closeValue, rsiValue, stochValue, stochRSIValue, cciValue, bbpValue, maValue, vwapValue, bbValue, stValue, regValue, msValue] = 
     collectData(timeframe(mst_indi_tf))

sentiment = math.avg(rsiValue, stochValue, stochRSIValue, cciValue, bbpValue, maValue, nz(vwapValue, 50), bbValue, stValue, regValue, msValue)

if barstate.islast
    processData(mst_panel_Show, mst_indi_tf, closeValue, rsiValue, stochValue, stochRSIValue, cciValue, bbpValue, maValue, nz(vwapValue, 50), bbValue, stValue, regValue, msValue, sentiment, 5.25 + mst_panel_Height, mst_panel_Height)

processMeter(mst_mrktMtr_Show and barstate.islast, sentiment)

tiRank = plot(mst_mrktOsc_Show ? mst_mrktOsc_height * sentiment / 100 : na, '(MST) Oscillator', color.from_gradient(mst_mrktOsc_height * sentiment, 0, mst_mrktOsc_height * 100, mst_bearColor, mst_bullColor), mst_mrktOsc_lnWidth, display = mst_display)

upperBand = plot(mst_mrktOsc_Show ? mst_mrktOsc_height * .75 : na, '(MST) Overbought Level', color.new(#787b86, 63), display = mst_display)
midLine   = plot(mst_mrktOsc_Show ? mst_mrktOsc_height *  .5 : na, '(MST) Equilibrium Level', color.new(#787b86, 63), display = mst_display)
lowerBand = plot(mst_mrktOsc_Show ? mst_mrktOsc_height * .25 : na, '(MST) Oversold Level', color.new(#787b86, 63), display = mst_display)

fill(tiRank, midLine, mst_mrktOsc_height *  1, mst_mrktOsc_height * .5, top_color = color.new(mst_bullColor,   0), bottom_color = color.new(mst_bullColor, 100), title = "(MST) Bullish Gradient Fill")
fill(tiRank, midLine, mst_mrktOsc_height * .5, mst_mrktOsc_height *  0, top_color = color.new(mst_bearColor, 100), bottom_color = color.new(mst_bearColor,   0), title = "(MST) Bearish Gradient Fill")

//---------------------------------------------------------------------------------------------------------------------}
// Calculations - Divergence Indicator (Build-in TradingView Script)
//---------------------------------------------------------------------------------------------------------------------{

[isRegular, isHidden] = switch mst_mrktOsc_Divergence
    'All' => [true, true]
    'Regular' => [true, false]
    'Hidden' => [false, true]
    => [false, false]

osc = mst_mrktOsc_height * sentiment / 100
lbR = 5
lbL = 5
rUpper = 60
rLower = 5

plFound = na(ta.pivotlow(osc, lbL, lbR)) ? false : true
phFound = na(ta.pivothigh(osc, lbL, lbR)) ? false : true

_inRange(cond) =>
    bars = ta.barssince(cond == true)
    rLower <= bars and bars <= rUpper

oscHL = osc[lbR] > ta.valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])
priceLL = low[lbR] < ta.valuewhen(plFound, low[lbR], 1)

bullCond = mst_mrktOsc_Show and isRegular and priceLL and oscHL and plFound

plot(plFound ? osc[lbR] : na, '(MST) Regular Bullish', bullCond ? mst_bullColor : color(na), 2, offset = -lbR, display = mst_display)
plotshape(bullCond ? osc[lbR] : na, '(MST) Regular Bullish Label', shape.labelup, location.absolute, color.new(mst_bullColor, 0), -lbR, display = mst_display)

oscLL = osc[lbR] < ta.valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])
priceHL = low[lbR] > ta.valuewhen(plFound, low[lbR], 1)

hiddenBullCond = mst_mrktOsc_Show and isHidden and priceHL and oscLL and plFound

plot(plFound ? osc[lbR] : na, '(MST) Hidden Bullish', hiddenBullCond ? color.new(mst_bullColor, 73) : color(na), 2, offset = -lbR, display = mst_display)
plotshape(hiddenBullCond ? osc[lbR] : na, '(MST) Hidden Bullish Label', shape.labelup, location.absolute, color.new(mst_bullColor, 73), -lbR, display = mst_display)

oscLH = osc[lbR] < ta.valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])
priceHH = high[lbR] > ta.valuewhen(phFound, high[lbR], 1)

bearCond = mst_mrktOsc_Show and isRegular and priceHH and oscLH and phFound

plot(phFound ? osc[lbR] : na, '(MST) Regular Bearish', bearCond ? mst_bearColor : color(na), 2, offset = -lbR, display = mst_display)
plotshape(bearCond ? osc[lbR] : na, '(MST) Regular Bearish Label', shape.labeldown, location.absolute, color.new(mst_bearColor, 0), -lbR, display = mst_display)

oscHH = osc[lbR] > ta.valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])
priceLH = high[lbR] < ta.valuewhen(phFound, high[lbR], 1)

hiddenBearCond = mst_mrktOsc_Show and isHidden and priceLH and oscHH and phFound

plot(phFound ? osc[lbR] : na, '(MST) Hidden Bearish', hiddenBearCond ? color.new(mst_bearColor, 73) : color(na), 2, offset = -lbR, display = mst_display)
plotshape(hiddenBearCond ? osc[lbR] : na, '(MST) Hidden Bearish Label', shape.labeldown, location.absolute, color.new(mst_bearColor, 73), -lbR, display = mst_display)

//Plot to fix market sentiment location
plot(5.25 + mst_panel_Height + mst_panel_Height + .5, color = na, editable = false)

//---------------------------------------------------------------------------------------------------------------------}

//------ END Market Sentiment Technicals ------//