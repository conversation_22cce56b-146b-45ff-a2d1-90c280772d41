//------ START ZLSMA - Zero Lag LSMA ------//

// indicator(title='ZLSMA - Zero Lag LSMA', shorttitle='ZLSMA', overlay=true, timeframe='')
zlsma_length = input(title='Length', defval=100, group="ZLSMA SETTINGS")
zlsma_offset = input(title='Offset', defval=0, group="ZLSMA SETTINGS")
zlsma_src = input(close, title='Source', group="ZLSMA SETTINGS")
zlsma_lsma = ta.linreg(zlsma_src, zlsma_length, zlsma_offset)
zlsma_lsma2 = ta.linreg(zlsma_lsma, zlsma_length, zlsma_offset)
zlsma_eq = zlsma_lsma - zlsma_lsma2
zlsma_zlsma = zlsma_lsma + zlsma_eq

plot(zlsma_zlsma, '(ZLSMA) Line', color=#706fd3, linewidth=3)

//------ END ZLSMA - Zero Lag LSMA ------//