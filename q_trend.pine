//------ START Q-Trend ------//

//indicator("Q-Trend", overlay = 1) 

// Inputs
qt_src = input(close, "Source", group = "Q-TREND SETTINGS")
qt_p = input.int(200, "Trend period", group = "Q-TREND SETTINGS", tooltip = "Changes STRONG signals' sensitivity.", minval = 1)
qt_atr_p = input.int(14, "ATR Period", group = "Q-TREND SETTINGS", minval = 1)
qt_mult = input.float(1.0, "ATR Multiplier", step = 0.1, group = "Q-TREND SETTINGS", tooltip = "Changes sensitivity: higher period = higher sensitivty.")
qt_mode = input.string("Type A", "Signal qt_mode", options = ["Type A", "Type B"], group = "Q-TREND SETTINGS")
qt_use_ema_smoother = input.string("No", "Smooth source with EMA?", options = ["Yes", "No"], group = "Q-TREND SETTINGS")
qt_src_ema_period = input(3, "EMA Smoother period", group = "Q-TREND SETTINGS")
qt_color_bars = input(false, "Color bars?", group = "Q-TREND SETTINGS")
qt_show_tl = input(false, "Show trend line?", group = "Q-TREND SETTINGS")
qt_signals_view = input.string("All", "Signals to show", options = ["All", "Up/Down", "Long/Short", "None"], group = "Q-TREND SETTINGS")
qt_signals_shape = input.string("Labels", "Signal's shape", options = ["Labels", "Arrows"], group = "Q-TREND SETTINGS")
qt_buy_col = input(#218c74, "Long colour", group = "Q-TREND SETTINGS", inline = "BS")
qt_sell_col = input(#b33939, "Short colour", group = "Q-TREND SETTINGS", inline = "BS")

// Calculations
qt_src := qt_use_ema_smoother == "Yes" ? ta.ema(qt_src, qt_src_ema_period) : qt_src // Source;

qt_h = ta.highest(qt_src, qt_p) // Highest of qt_src p-bars back;
qt_l = ta.lowest(qt_src, qt_p) // Lowest of qt_src p-bars back.
qt_d = qt_h - qt_l

qt_ls = "" // Tracker of last signal

qt_m = (qt_h + qt_l) / 2 // Initial trend line;
qt_m := bar_index > qt_p ? qt_m[1] : qt_m

qt_atr = ta.atr(qt_atr_p)[1] // ATR;
qt_epsilon = qt_mult * qt_atr // Epsilon is a mathematical variable used in many different theorems in order to simplify work with mathematical object. Here it used as sensitivity measure.

qt_change_up = (qt_mode == "Type B" ? ta.cross(qt_src, qt_m + qt_epsilon) : ta.crossover(qt_src, qt_m + qt_epsilon)) or qt_src > qt_m + qt_epsilon // If price breaks trend line + qt_epsilon (so called higher band), then it is time to update the value of a trend line;
qt_change_down = (qt_mode == "Type B" ? ta.cross(qt_src, qt_m - qt_epsilon) : ta.crossunder(qt_src, qt_m - qt_epsilon)) or qt_src < qt_m - qt_epsilon // If price breaks trend line - qt_epsilon (so called higher band), then it is time to update the value of a trend line.
sb = open < qt_l + qt_d / 8 and open >= qt_l
ss = open > qt_h - qt_d / 8 and open <= qt_h
qt_strong_buy = sb or sb[1] or sb[2] or sb[3] or sb[4]
qt_strong_sell = ss or ss[1] or ss[2] or ss[3] or ss[4]

qt_m := (qt_change_up or qt_change_down) and qt_m != qt_m[1] ? qt_m : qt_change_up ? qt_m + qt_epsilon : qt_change_down ? qt_m - qt_epsilon : nz(qt_m[1], qt_m) // Updating the trend line.

qt_ls := qt_change_up ? "B" : qt_change_down ? "S" : qt_ls[1] // Last signal. Helps avoid multiple labels in a row with the same signal;
qt_colour = qt_ls == "B" ? qt_buy_col : qt_sell_col // Colour of the trend line.
qt_buy_shape   = qt_signals_shape == "Labels" ? shape.labelup     : shape.triangleup
qt_sell_shape  = qt_signals_shape == "Labels" ? shape.labeldown   : shape.triangledown

// Plottings
plot(qt_show_tl ? qt_m : na, "(QT) trend line", qt_colour, 3) // Plotting the trend line.

// Signals with label shape
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_up and qt_ls[1] != "B" and not qt_strong_buy, "(QT) Long signal"       , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny, text = "↑", textcolor = color.white)      // Plotting the LONG signal;
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_down and qt_ls[1] != "S" and not qt_strong_sell, "(QT) Short signal"   , color = qt_colour, style = qt_sell_shape, size = size.tiny, text = "↓", textcolor = color.white)                                   // Plotting the SHORT signal.
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_up and qt_ls[1] != "B" and qt_strong_buy, "(QT) Strong Long signal"      , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny, text = "LONG", textcolor = color.white)   // Plotting the STRONG LONG signal;
plotshape(qt_signals_shape == "Labels" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_down and qt_ls[1] != "S" and qt_strong_sell, "(QT) Strong Short signal"  , color = qt_colour, style = qt_sell_shape, size = size.tiny, text = "SHORT", textcolor = color.white)                                 // Plotting the STRONG SHORT signal.

// Signal with arrow shape
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_up and qt_ls[1] != "B" and not qt_strong_buy, "(QT) Long signal"       , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny) // Plotting the LONG signal;
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Up/Down") and qt_change_down and qt_ls[1] != "S" and not qt_strong_sell, "(QT) Short signal"   , color = qt_colour, style = qt_sell_shape, size = size.tiny)                               // Plotting the SHORT signal.
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_up and qt_ls[1] != "B" and qt_strong_buy, "(QT) Strong Long signal"      , color = qt_colour, style = qt_buy_shape , location = location.belowbar, size = size.tiny) // Plotting the STRONG LONG signal;
plotshape(qt_signals_shape == "Arrows" and (qt_signals_view == "All" or qt_signals_view == "Long/Short") and qt_change_down and qt_ls[1] != "S" and qt_strong_sell, "(QT) Strong Short signal"  , color = qt_colour, style = qt_sell_shape, size = size.tiny)                               // Plotting the STRONG SHORT signal.

barcolor(qt_color_bars ? qt_colour : na, title="(QT) Bar Colors") // Bar coloring

// Alerts
// alertcondition(qt_change_up and qt_ls[1] != "B", "Q-Trend LONG", "Q-Trend LONG signal were given.") // Long alert.
// alertcondition(qt_change_down and qt_ls[1] != "S", "Q-Trend SHORT", "Q-Trend SHORT signal were given.") // Short alert.
// alertcondition((qt_change_up and qt_ls[1] != "B") or (qt_change_down and qt_ls[1] != "S"), "Q-Trend Signal", "Q-Trend gave you a signal!")
// alertcondition(qt_change_up and qt_ls[1] != "B" and qt_strong_buy, "Strong LONG signal", "Q-Trend gave a Strong Long signal!")
// alertcondition(qt_change_down and qt_ls[1] != "S" and qt_strong_sell, "Strong SHORT signal", "Q-Trend gave a Strong Short signal!")

//------ END Q-Trend ------//